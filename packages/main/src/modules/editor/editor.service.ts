import { Inject, Injectable } from '@nestjs/common'
import { <PERSON><PERSON>, EditorIPCClient, RenderRequestPayload } from '@app/shared/types/ipc/editor.js'
import { KeyframeExtractorService } from './keyframe-extractor.service.js'
import { FileUploaderService } from '../file-uploader/file-uploader.service.js'
import { RequestService } from '../global/request.service.js'
import { promises as fs } from 'fs'
import { join } from 'path'
import { tmpdir } from 'os'
import { v4 as uuidv4 } from 'uuid'
import JSZip from 'jszip'
import ffmpeg from 'fluent-ffmpeg'

const ZERO_SIMILARITY_COMBO_GENERATING_STRATEGY = 2

@Injectable()
export class EditorService implements EditorIPCClient {

  constructor(
    @Inject(KeyframeExtractorService) private keyframeExtractorService: KeyframeExtractorService,
    @Inject(FileUploaderService) private fileUploaderService: FileUploaderService,
    @Inject(RequestService) private requestService: RequestService
  ) {
    console.log('EditorService initialized', keyframeExtractorService, fileUploaderService, requestService)
  }

  /**
   * 压缩 EditorState 对象为二进制数据
   * @param editorState 编辑器状态对象
   * @returns Promise<Buffer> 压缩后的二进制数据
   */
  async compressEditorState(editorState: any): Promise<Buffer> {
    let tempJsonPath: string | null = null

    try {
      // 1. 生成唯一的文件名
      const uuid = uuidv4()
      const fileName = `${uuid}.json`
      tempJsonPath = join(tmpdir(), fileName)

      // 2. 将 EditorState 对象序列化为 JSON 字符串并写入临时文件
      const jsonString = JSON.stringify(editorState, null, 2)
      await fs.writeFile(tempJsonPath, jsonString, 'utf8')

      // 3. 创建 ZIP 压缩包
      const zip = new JSZip()
      const fileContent = await fs.readFile(tempJsonPath, 'utf8')
      zip.file(fileName, fileContent)

      // 4. 生成压缩包的 Buffer
      const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' })

      // console.log(`[EditorService] 压缩完成: 原始大小 ${jsonString.length} 字节, 压缩后 ${zipBuffer.length} 字节`)

      return zipBuffer
    } catch (error) {
      console.error('[EditorService] 压缩 EditorState 失败:', error)
      throw new Error('压缩编辑器状态失败')
    } finally {
      // 5. 清理临时文件
      if (tempJsonPath) {
        try {
          await fs.unlink(tempJsonPath)
        } catch (cleanupError) {
          console.warn('[EditorService] 清理临时文件失败:', cleanupError)
        }
      }
    }
  }

  /**
   * 解压二进制数据为 EditorState 对象
   * @param compressedData 压缩的二进制数据（可以是 Buffer、Uint8Array 或 number[]）
   * @returns Promise<any> 解压后的 EditorState 对象
   */
  async decompressEditorState(compressedData: any): Promise<any> {
    try {
      let buffer: Buffer

      // 处理不同类型的输入数据
      if (Buffer.isBuffer(compressedData)) {
        buffer = compressedData
      } else if (compressedData instanceof Uint8Array) {
        buffer = Buffer.from(compressedData)
      } else if (Array.isArray(compressedData)) {
        // 处理从云端 API 返回的 number[] 格式
        buffer = Buffer.from(compressedData)
      } else {
        throw new Error('不支持的数据格式')
      }

      // 1. 使用 JSZip 解压缩包
      const zip = new JSZip()
      const zipContent = await zip.loadAsync(buffer)

      // 2. 获取压缩包中的第一个 .json 文件
      const jsonFiles = Object.keys(zipContent.files).filter(name => name.endsWith('.json'))
      if (jsonFiles.length === 0) {
        throw new Error('压缩包中未找到 JSON 文件')
      }

      const jsonFileName = jsonFiles[0]
      const jsonFile = zipContent.files[jsonFileName]

      // 3. 读取 JSON 文件内容
      const jsonString = await jsonFile.async('text')

      // 4. 解析 JSON 字符串为对象
      const editorState = JSON.parse(jsonString)
      // console.log(`[EditorService] 解压完成: 压缩大小 ${buffer.length} 字节, 解压后 ${jsonString.length} 字节`)

      return editorState
    } catch (error) {
      console.error('[EditorService] 解压 EditorState 失败:', error)
      throw new Error('解压编辑器状态失败')
    }
  }

  /**
   * 算法目标：
   * 1. 给定的 `matrix` 是一个二维数组，代表了每一列上可用的行序号。例如, [[0,1,2], [1,2]] 代表 第一列可用行号为0,1,2; 第二列可用行号为1,2
   * 2. 相似度的定义: 两个组合之间相似度，指他们在同样的位置上出现同样值的频次。例如, [0,1] 与 [0,2] 的相似度为 0.5。
   * 3. 计算一个组合的相似度，需要将它与先前的所有组合进行比较，取最大值。
   * 4. 生成的组合必须满足 `matrix` 的约束，即每一列上的值必须在 `matrix` 中的对应数组内。
   * 5. 生成的组合必须是唯一的，不能重复。
   * 6. 在开始生成之前，需要对 `limit` 的合法性进行校验，如果 `limit` 大于所有可能的组合数，则需要自动设置为最大值。
   * 7. 优先生成相似度为 0 的组合。例如给定的 matrix 为 [[0,1,2], [0,1,2], [0,1,2]], 则可以优先生成三个相似度为 0 的组合, 即 [0,0,0], [1,1,1], [2,2,2]
   *    当然，以上的三个并不是唯一的相似度的为 0 的组合，例如 [0,1,2], [1,2,0], [2,0,1] 也是相似度为 0 的组合。算法要同时允许这两种策略，通过一个参数来控制。
   * 8. 相似度为 0 的组合生成完之后，以随机生成的方式继续生成，直到组合总数达到 `limit` 数量为止。
   */
  async generateCombos(props: { limit: number, threshold: number, matrix: number[][] }): Promise<Combo[]> {
    const { limit, threshold, matrix } = props

    const _checkSimilarity = (a: number[], b: number[]) => {
      let count = 0
      for (let i = 0; i < a.length; i++) {
        if (a[i] === b[i]) {
          count++
        }
      }
      return count / a.length
    }

    // 输入验证
    if (!matrix || matrix.length === 0) {
      return []
    }

    // 计算所有可能的组合总数
    const totalPossibleCombos = matrix.reduce((total, column) => total * column.length, 1)

    // 调整limit为合法值
    const actualLimit = Math.min(limit, totalPossibleCombos)

    if (actualLimit <= 0) {
      return []
    }

    const results: Combo[] = []
    const usedCombinations = new Set<string>()

    // 辅助函数：计算组合与已有组合的最大相似度
    const getMaxSimilarity = (combo: number[]): number => {
      if (results.length === 0) return 0
      return Math.max(...results.map(existing => _checkSimilarity(combo, existing.combination)))
    }

    // 辅助函数：组合转字符串用于去重
    const comboToString = (combo: number[]): string => combo.join(',')

    // 辅助函数：添加组合到结果
    const addCombo = (combo: number[]): boolean => {
      const comboStr = comboToString(combo)
      if (usedCombinations.has(comboStr)) {
        return false
      }

      const maxSimilarity = getMaxSimilarity(combo)
      if (maxSimilarity < threshold) {
        usedCombinations.add(comboStr)
        results.push({
          combination: [...combo],
          similarity: maxSimilarity
        })
        return true
      }
      return false
    }

    // 策略1：横排策略 - 生成相似度为0的组合 [0,0,0], [1,1,1], [2,2,2]
    const generateHorizontalZeroSimilarity = (): void => {
      // 找到所有列的交集值
      const commonValues = matrix[0].filter(value =>
        matrix.every(column => column.includes(value))
      )

      for (const value of commonValues) {
        if (results.length >= actualLimit) break
        const combo = new Array(matrix.length).fill(value)
        addCombo(combo)
      }
    }

    // 策略2：轮换策略 - 生成相似度为0的组合 [0,1,2], [1,2,0], [2,0,1]
    const generateRotationZeroSimilarity = (): void => {
      // 找到最小列长度
      const minColumnLength = Math.min(...matrix.map(col => col.length))

      // 尝试生成轮换组合
      for (let offset = 0; offset < minColumnLength; offset++) {
        if (results.length >= actualLimit) break

        const combo: number[] = []
        let isValid = true

        for (let i = 0; i < matrix.length; i++) {
          const targetIndex = (i + offset) % minColumnLength
          if (targetIndex >= matrix[i].length) {
            isValid = false
            break
          }
          combo.push(matrix[i][targetIndex])
        }

        if (isValid) {
          addCombo(combo)
        }
      }
    }

    // 策略3：随机挑选相似度为0的组合
    const generateRandomZeroSimilarity = (): void => {
      const maxAttempts = Math.min(1000, totalPossibleCombos)
      let attempts = 0

      while (results.length < actualLimit && attempts < maxAttempts) {
        attempts++

        // 生成随机组合
        const combo = matrix.map(column =>
          column[Math.floor(Math.random() * column.length)]
        )

        // 检查是否与现有组合相似度为0
        if (results.length === 0 || getMaxSimilarity(combo) === 0) {
          if (addCombo(combo)) {
            attempts = 0 // 重置尝试次数
          }
        }
      }
    }

    // 第一阶段：生成相似度为0的组合
    [
      generateHorizontalZeroSimilarity,
      generateRotationZeroSimilarity,
      generateRandomZeroSimilarity
    ][ZERO_SIMILARITY_COMBO_GENERATING_STRATEGY]()

    // 第二阶段：随机生成剩余组合
    const maxRandomAttempts = Math.min(10000, totalPossibleCombos * 2)
    let randomAttempts = 0

    while (results.length < actualLimit && randomAttempts < maxRandomAttempts) {
      randomAttempts++

      // 生成随机组合
      const combo = matrix.map(column =>
        column[Math.floor(Math.random() * column.length)]
      )

      addCombo(combo)
    }

    return results
  }

  async extractVideoKeyFrames(props: { src: string; }): Promise<{ frameNumber: number; dataUrl: string; }[]> {
    const { src } = props

    return this.keyframeExtractorService.extractKeyframes(src)
  }

  /**
   * 上传混剪结果到服务器
   * @param params 上传参数
   * @returns Promise<any> 上传结果
   */
  async uploadMixcutResult(params: {
    scriptId: string
    data: RenderRequestPayload
    similarity: number
    cover?: string
    duration?: number
  }): Promise<any> {
    try {
      const mixcutId = `mixcut-${uuidv4()}`
      const folderUuid = 'mixcut'

      const { scriptId, data, cover, duration, similarity } = params

      const coverUrl = cover
        ? await this.fileUploaderService
          .uploadBufferToOSS({
            buffer: Buffer.from(cover.replace(/data:image\/\w+;base64,/, ''), 'base64'),
            fileName: `${mixcutId}-preview.jpg`,
            folderUuid
          })
          .then(r => r.url)
        : null

      const jsonString = JSON.stringify(data)
      const buffer = Buffer.from(jsonString, 'utf8')

      const uploadResult = await this.fileUploaderService.uploadBufferToOSS({
        buffer: Array.from(buffer),
        fileName: `${mixcutId}.json`,
        folderUuid
      })

      if (!uploadResult.success) {
        throw new Error(`上传预览数据失败: ${uploadResult.error}`)
      }

      if (!uploadResult.objectId) {
        throw new Error('上传结果中缺少 objectId')
      }

      const result = await this.requestService.post('/app-api/creative/remix/preview/save', {
        scriptId,
        cover: coverUrl,
        duration: duration || 0,
        repetitionRate: similarity,
        objectId: uploadResult.objectId
      })
      console.log('[EditorService] 混剪结果上传成功:', result)

      return result
    } catch (error) {
      console.error('[EditorService] 上传混剪结果失败:', error)
      throw new Error(`上传混剪结果失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取音频文件的时长
   * @param audioUrl 音频文件URL
   * @returns Promise<number> 音频时长（秒）
   */
  async getAudioDuration(audioUrl: string): Promise<number> {
    let tempFilePath: string | null = null

    try {
      // 1. 下载音频文件到临时目录
      const response = await fetch(audioUrl)
      if (!response.ok) {
        throw new Error(`下载音频文件失败: ${response.status} ${response.statusText}`)
      }

      const audioBuffer = await response.arrayBuffer()
      const uuid = uuidv4()
      const tempFileName = `${uuid}.mp3`
      tempFilePath = join(tmpdir(), tempFileName)

      await fs.writeFile(tempFilePath, Buffer.from(audioBuffer))

      // 2. 使用 fluent-ffmpeg 获取音频时长
      const duration = await new Promise<number>((resolve, reject) => {
        ffmpeg.ffprobe(tempFilePath!, (err, metadata) => {
          if (err) {
            reject(new Error(`ffprobe 错误: ${err.message}`))
            return
          }

          const duration = metadata.format?.duration
          if (typeof duration !== 'number' || duration <= 0) {
            reject(new Error('无法获取有效的音频时长'))
            return
          }

          resolve(duration)
        })
      })

      console.log(`[EditorService] 获取音频时长成功: ${audioUrl} -> ${duration}秒`)
      return duration
    } catch (error) {
      console.error('[EditorService] 获取音频时长失败:', error)

      // 如果获取时长失败，尝试备用方法
      if (tempFilePath) {
        try {
          const stats = await fs.stat(tempFilePath)
          const fileSizeKB = stats.size / 1024
          // 假设平均比特率为 128kbps
          const estimatedDuration = (fileSizeKB * 8) / 128

          console.log(`[EditorService] 使用备用方法估算音频时长: ${estimatedDuration}秒`)
          return Math.max(1, estimatedDuration) // 至少1秒
        } catch (statError) {
          console.warn('[EditorService] 备用方法也失败:', statError)
        }
      }

      // 如果所有方法都失败，返回默认时长
      console.warn('[EditorService] 使用默认时长估算')
      return 3 // 默认3秒
    } finally {
      // 清理临时文件
      if (tempFilePath) {
        try {
          await fs.unlink(tempFilePath)
        } catch (cleanupError) {
          console.warn('[EditorService] 清理临时文件失败:', cleanupError)
        }
      }
    }
  }
}
