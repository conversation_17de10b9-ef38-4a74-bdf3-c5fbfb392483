import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'
import { Injectable } from '@nestjs/common'
import { app, BrowserWindow } from 'electron'

@Injectable()
export class WindowManagerIpcHandler extends BaseIPCHandler<'windowManager'> {

  protected readonly platformPrefix = 'windowManager'

  constructor() {
    super()
  }

  registerAll() {
    this.registerHandler('minimize', () => {
      const win = BrowserWindow.getFocusedWindow()
      if (win) {
        win.minimize()
      }
    })

    // 最大化窗口或还原窗口
    this.registerHandler('maximize', () => {
      const win = BrowserWindow.getFocusedWindow()
      if (!win) return false

      if (win.isMaximized()) {
        win.unmaximize()
        return false
      } else {
        win.maximize()
        return true
      }
    })

    // 关闭窗口并退出应用
    this.registerHandler('close', () => {
      app.quit()
    })
  }
}
