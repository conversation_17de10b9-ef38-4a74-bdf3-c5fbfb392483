import React from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { Loader2Icon, PlusIcon } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { useResource } from '@rve/editor/hooks/resource/useResource.tsx'
import { useQueryClient } from '@tanstack/react-query'

export interface ResourceCacheIndicatorProps {
  /**
   * 资源类型
   */
  resourceType: ResourceType
  /**
   * 资源URL
   */
  resourceUrl: string

  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 图标大小
   */
  size?: number
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 点击下载按钮的回调
   */
  onDownload?: () => void
  version?: string

  /**
   * 资源是否已缓存
   * 组件完全依赖此外部状态，不进行内部查询
   */
  isCached?: boolean
  /**
   * 是否正在检查缓存状态
   * 组件完全依赖此外部状态，不进行内部查询
   */
  isChecking?: boolean

}

/**
 * 资源缓存状态指示器
 * 显示资源是否已缓存到本地
 *
 * 注意：此组件完全依赖外部传入的状态（isCached, isChecking, isLoading），
 * 不进行内部查询。状态刷新通过 queryClient.invalidateQueries 实现。
 */
export function ResourceCacheIndicator({
  resourceType,
  resourceUrl,
  isLoading: externalLoading = false,
  size = 16,
  className,
  onDownload,
  version,
  isCached: externalIsCached = false,
  isChecking: externalIsChecking = false
}: ResourceCacheIndicatorProps) {
  const { downloadResourceToCache, setResourceLoadingState } = useResource()
  const queryClient = useQueryClient()

  // 完全依赖外部传入的状态
  const isCached = externalIsCached
  const checking = externalIsChecking
  const isLoading = externalLoading

  const handleDownload = async (e: React.MouseEvent) => {
    e.stopPropagation()

    if (onDownload) {
      onDownload()
    } else {
      try {
        setResourceLoadingState(resourceType, resourceUrl, true)

        // 下载资源，为音乐资源添加 customExt 参数
        await downloadResourceToCache({
          url: resourceUrl,
          resourceType,
          version: version || '1.0.0',
          // 如果是音乐资源，添加 mp3 扩展名
          customExt: resourceType === ResourceType.MUSIC ? 'mp3' : undefined
        })

        // 使用 queryClient.invalidateQueries 刷新相关状态
        // 这将触发父组件重新获取缓存状态
        queryClient.invalidateQueries({
          predicate: query => {
            // 刷新所有与此资源相关的查询
            return query.queryKey.includes(resourceType) || query.queryKey.includes(resourceUrl)
          }
        })
      } catch (error) {
        console.error('下载资源失败:', error)
        // 确保在出错时也重置加载状态
        setResourceLoadingState(resourceType, resourceUrl, false)
      }
    }
  }

  return (
    <div
      className={cn(
        'flex items-center justify-center bg-gray-900/80 rounded p-1 cursor-pointer text-gray-500 hover:bg-gray-900/50 transition-all',
        className
      )}
    >
      {checking ? (
        <Loader2Icon
          className="animate-spin text-gray-400 cursor-pointer"
          style={{ width: size, height: size }}
        />
      ) : isLoading ? (
        <Loader2Icon
          className="animate-spin text-blue-500 cursor-pointer"
          style={{ width: size, height: size }}
        />
      ) : isCached ? (
        <button
          onClick={handleDownload}
        >
          <PlusIcon
            className="hover:text-green-400 text-green-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        </button>

      ) : (
        <button
          onClick={handleDownload}
        >
          <PlusIcon
            className="text-gray-400 hover:text-blue-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        </button>
      )}
    </div>
  )
}
