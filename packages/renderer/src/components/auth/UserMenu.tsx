import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { TokenManager } from '@/libs/storage'
import { useToast } from '@/hooks/useToast'
import { User2Icon, LogOut, Settings, User } from 'lucide-react'

interface UserMenuProps {
  /**
   * 用户名，如果不提供则显示默认文本
   */
  username?: string
  /**
   * 自定义触发器类名
   */
  triggerClassName?: string
}

/**
 * 用户菜单组件
 * 提供用户相关操作，包括退出登录等功能
 */
export const UserMenu: React.FC<UserMenuProps> = ({
  username = '用户名',
  triggerClassName = ''
}) => {
  const navigate = useNavigate()
  const { toast } = useToast()

  // 获取用户信息
  const userId = TokenManager.getUserId()
  // const isLoggedIn = TokenManager.isLoggedIn()

  // 退出确认对话框状态
  const [showLogoutDialog, setShowLogoutDialog] = useState(false)

  // 显示退出确认对话框
  const handleLogoutClick = () => {
    setShowLogoutDialog(true)
  }

  // 确认退出登录
  const handleConfirmLogout = () => {
    try {
      // 清除认证数据
      TokenManager.clearAuthData()

      // 关闭对话框
      setShowLogoutDialog(false)

      // 显示退出成功提示
      toast({
        title: '退出成功',
        description: '您已安全退出登录'
      })

      // 跳转到登录页
      navigate('/login', { replace: true })
    } catch (error) {
      console.error('退出登录失败:', error)
      toast({
        title: '退出失败',
        description: '退出登录时发生错误，请重试',
        variant: 'destructive'
      })
    }
  }

  // 取消退出登录
  const handleCancelLogout = () => {
    setShowLogoutDialog(false)
  }

  // 处理个人设置
  const handleSettings = () => {
    // TODO: 实现个人设置页面
    toast({
      title: '功能开发中',
      description: '个人设置功能正在开发中'
    })
  }

  // 处理个人资料
  const handleProfile = () => {
    // TODO: 实现个人资料页面
    toast({
      title: '功能开发中',
      description: '个人资料功能正在开发中'
    })
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className={`flex items-center space-x-2 p-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors focus:outline-none focus:ring-2 focus:ring-primary/20 ${triggerClassName}`}
          aria-label="用户菜单"
        >
          <User2Icon className="w-4 h-4 text-muted-foreground" />
          <span className="text-xs text-muted-foreground truncate max-w-20">{username}</span>
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>我的账户</DropdownMenuLabel>
        {userId && (
          <div className="px-2 py-1.5 text-xs text-muted-foreground">
            用户ID: {userId}
          </div>
        )}
        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={handleProfile}>
          <User className="mr-2 h-4 w-4" />
          <span>个人资料</span>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={handleSettings}>
          <Settings className="mr-2 h-4 w-4" />
          <span>设置</span>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={handleLogoutClick}
          className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>退出登录</span>
        </DropdownMenuItem>
      </DropdownMenuContent>

      {/* 退出确认对话框 */}
      <Dialog open={showLogoutDialog} onOpenChange={setShowLogoutDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认退出登录</DialogTitle>
            <DialogDescription>
              您确定要退出登录吗？退出后需要重新输入手机号和验证码才能登录。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelLogout}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleConfirmLogout}>
              确认退出
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DropdownMenu>
  )
}

export default UserMenu
