import React from 'react'
import { RouterProvider, createMemoryRouter } from 'react-router-dom'
import { BaseLayout } from './BaseLayout'
import {
  Grid3X3
} from 'lucide-react'
import type { MenuItem } from './HomeLayout'

// Matrix 专用的布局组件，支持路由
const MatrixLayoutWithRouter: React.FC = () => {
  // Matrix 页面专用的菜单项定义
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      title: '矩阵概览',
      icon: <Grid3X3 />,
      path: '/matrix/dashboard',
    },
    
  ]

  return (
    <BaseLayout menuItems={menuItems} />
  )
}

// 创建 Matrix 专用的路由配置
const createMatrixRoutes = () => [
  {
    path: '/matrix',
    element: <MatrixLayoutWithRouter />,
    children: [
      {
        index: true,
        element: <div />,
      },
      {
        path: 'dashboard',
        element: <div />,
      },
    ],
  },
]

export const MatrixLayout: React.FC = () => {
  // 创建 Matrix 专用的内存路由器，避免与浏览器 URL 冲突
  const router = React.useMemo(() => {
    return createMemoryRouter(createMatrixRoutes(), {
      initialEntries: ['/matrix/dashboard'], // 设置初始路径
      initialIndex: 0,
    })
  }, [])

  return (
    <div className="h-full w-full">
      <RouterProvider router={router} />
    </div>
  )
}
