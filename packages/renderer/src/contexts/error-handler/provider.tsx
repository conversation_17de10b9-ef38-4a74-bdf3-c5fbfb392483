import React, { ReactNode, useEffect, useState } from 'react'
import { Slide, ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import errorHandler from '@/libs/tools/ErrorHandler.ts'
import { ErrorHandlerOptions, ErrorInfo, ErrorType } from '@/types/error'
import { ErrorContextType, ErrorHandlerContext } from './context'

interface ErrorProviderProps {
  children: ReactNode
}

export const ErrorHandlerProvider: React.FC<ErrorProviderProps> = ({ children }) => {
  const [lastError, setLastError] = useState<ErrorInfo | null>(null)

  const handleError = (error: unknown, options?: ErrorHandlerOptions): ErrorInfo => {
    const errorInfo = errorHandler.handleError(error, options)
    setLastError(errorInfo)
    return errorInfo
  }

  const handleBusinessError = (code: number, options?: ErrorHandlerOptions): ErrorInfo => {
    const errorInfo = errorHandler.handleBusinessError(code, options)
    setLastError(errorInfo)
    return errorInfo
  }

  const createError = (
    message: string,
    code: string | number,
    type: ErrorType = ErrorType.UNKNOWN,
    options?: ErrorHandlerOptions
  ): ErrorInfo => {
    const errorInfo = errorHandler.createErrorInfo(message, code, type, options)

    // 处理错误（显示提示、上报）
    handleError(errorInfo)

    return errorInfo
  }

  // 清除最近的错误
  const clearLastError = () => {
    setLastError(null)
  }

  // 安装全局错误处理器
  useEffect(() => {
    errorHandler.installGlobalHandlers()
  }, [])

  const contextValue: ErrorContextType = {
    handleError,
    handleBusinessError,
    createError,
    lastError,
    clearLastError
  }

  return (
    <ErrorHandlerContext.Provider value={contextValue}>
      {children}
      <ToastContainer
        position="top-center"
        autoClose={3000}
        newestOnTop
        closeOnClick
        rtl={false}
        draggable
        pauseOnHover={false}
        theme="dark"
        hideProgressBar
        transition={Slide}

      />
    </ErrorHandlerContext.Provider>
  )
}
