import { QUERY_KEYS } from '../../constants/queryKeys'
import { ResourceModule } from '../../libs/request/api/resource'
import { MaterialResource } from '@/types/resources'
import { useQuery } from '@tanstack/react-query'
import { TreeNode, buildTreeFromFlatList } from '@/components/TreeList'
import { useInfiniteQuery } from '../useInfiniteQuery'

/**
 * 核心逻辑：获取目录并转换为树
 */
export const getMaterialDirectoryTree = async (
  params: MaterialResource.MaterialDirectoryParams,
) => {
  if (!params.projectId) return []
  const flatList = await ResourceModule.directory.list(params)
  return buildTreeFromFlatList(flatList)
}

/**
 * 获取素材目录列表
 * @param params 查询参数（项目ID和关键词）
 * @param options enabled 或其他行为
 */
export const useQueryMaterialDirectoryList = (
  params: MaterialResource.MaterialDirectoryParams,
  options?: any,
) => {
  return useQuery<TreeNode[], Error>({
    queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST, params],
    queryFn: () => getMaterialDirectoryTree(params),
    initialData: undefined,
    staleTime: 0,
    ...options,
  })
}

/**
 * 获取素材目录树（非 Hook 形式）
 * @param params 包含 projectId 和 keyword
 */
export const fetchMaterialDirectoryList = async (
  params: MaterialResource.MaterialDirectoryParams,
) => {
  return getMaterialDirectoryTree(params)
}

/**
 * 使用无限查询获取素材媒体文件列表，支持无限滚动加载
 * @param params 查询参数，包括分类ID和每页大小
 * @returns 无限查询结果
 */
export const useQueryMediaList = (params: any, enabled: boolean) => {
  return useInfiniteQuery(
    [QUERY_KEYS.MATERIAL_MEDIA_LIST],
    ResourceModule.media.list,
    params,
    { enabled }
  )
}

/**
 * 使用无限查询获取媒体文件回收站列表，支持无限滚动加载
 * @param params 查询参数，包括分类ID和每页大小
 * @returns 无限查询结果
 */
export const useQueryMediaRecycleList = (params: any) => {
  return useInfiniteQuery<any>(
    [QUERY_KEYS.MATERIAL_MEDIA_RECYCLR_LIST],
    ResourceModule.media.recycleList,
    params,
    {
      pageSize: params.pageSize || 12,
      enabled: !!params.projectId,
    },
  )
}

/**
 * 获取文件夹回收站列表
 * @param params 查询参数，包括分类ID和每页大小
 */
export const useQueryDirRecycleList = (params: any, options?: any) => {
  return useQuery<MaterialResource.Directory[], Error>({
    queryKey: [QUERY_KEYS.MATERIAL_DIR_RECYCLR_LIST, params],
    queryFn: async () => {
      if (!params.projectId) {
        return Promise.resolve([])
      }

      return await ResourceModule.directory.recycleList(params)
    },
    initialData: [],
    staleTime: 0,
    ...options,
  })
}
