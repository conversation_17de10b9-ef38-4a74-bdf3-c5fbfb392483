import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { MatrixModule } from '@/libs/request/api/matrix'
import { usePagination } from '../usePagination'
import { PaginationParams } from '@app/shared/types/database.types'
import { AccountPushDetail, AccountPushDetailRequestParams, TimeRangeParams } from '@/types/matrix/douyin.ts'

/**
 * 获取抖音账号概览数据
 * @param params 时间范围参数
 * @param enabled 是否启用查询
 */
export const useQueryDyAccountOverview = (
  params?: TimeRangeParams,
) => {
  return useQuery({
    queryKey: [QUERY_KEYS.DY_ACCOUNT_OVERVIEW, params],
    queryFn: () => MatrixModule.dyAccount.overview(params),
  })
}

/**
 * 获取抖音推送计划列表（分页）
 * @param searchParams 搜索参数
 * @param initialPageSize 初始页面大小，默认10
 */
export const usePaginationDyPushPlanList = (
  searchParams: Omit<PaginationParams, 'page' | 'size'> = {},
  initialPageSize: number = 10
) => {
  return usePagination({
    queryKey: [QUERY_KEYS.DY_PUSH_PLAN_LIST],
    queryFn: params => MatrixModule.dyAccount.pushPlanList(params),
    searchParams,
    initialPageSize,
  })
}

export const usePaginationPlanDetailList = (
  searchParams: AccountPushDetailRequestParams = {},
  initialPageSize: number = 20,
  enabled?: boolean
) => {
  return usePagination<AccountPushDetail, AccountPushDetailRequestParams>({
    queryKey: [QUERY_KEYS.ACCOUNT_PUSH_DETAIL],
    queryFn: MatrixModule.dyAccount.accountPushDetail,
    searchParams: searchParams,
    initialPageSize,
    enabled: enabled ? enabled : true,
  })
}

