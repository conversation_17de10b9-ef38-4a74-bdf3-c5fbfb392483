import { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { PaginationState } from '@tanstack/react-table'
import { PaginatedResult, PaginationParams } from '@app/shared/types/database.types.ts'

/**
 * 分页查询函数类型
 */
export type PaginationQueryFn<T, P extends PaginationParams = PaginationParams> = (
  params: P
) => Promise<PaginatedResult<T>>

/**
 * 分页配置选项
 */
export interface UsePaginationOptions<T, P extends PaginationParams = PaginationParams> {
  queryKey: unknown[]
  queryFn: PaginationQueryFn<T, P>
  searchParams?: Omit<P, 'pageNo' | 'pageSize'>
  initialPageSize?: number
  enabled?: boolean
}

/**
 * 分页返回结果
 */
export interface UsePaginationResult<T> {
  data: T[]
  pagination: {
    pageIndex: number
    pageSize: number
    pageCount: number
    total: number
  }
  isLoading: boolean
  isError: boolean
  error: Error | null
  setPagination: (pagination: PaginationState) => void
  refetch: () => void
}

/**
 * 通用分页 Hook
 * 统一封装分页逻辑，支持类型推导
 */
export function usePagination<T, P extends PaginationParams = PaginationParams>({
  queryKey,
  queryFn,
  searchParams = {} as Omit<P, 'pageNo' | 'pageSize'>,
  initialPageSize = 10,
  enabled = true,
}: UsePaginationOptions<T, P>): UsePaginationResult<T> {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: initialPageSize,
  })

  // 构建查询参数
  const queryParams = useMemo(() => ({
    ...searchParams,
    pageNo: pagination.pageIndex + 1, // 后端页码从1开始
    pageSize: pagination.pageSize,
  } as P), [searchParams, pagination])

  // 使用 React Query 进行数据查询
  const {
    data: queryResult,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: [...queryKey, queryParams],
    queryFn: () => queryFn(queryParams),
    enabled,
  })

  // 计算分页信息
  const paginationInfo = useMemo(() => {
    const total = queryResult?.total || 0
    const pageCount = Math.ceil(total / pagination.pageSize)
    
    return {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
      pageCount,
      total,
    }
  }, [queryResult?.total, pagination])

  return {
    data: queryResult?.list || [],
    pagination: paginationInfo,
    isLoading,
    isError,
    error: error as Error | null,
    setPagination,
    refetch,
  }
}
