import { SubCacheManager } from '../types'

export interface StickerLoadingState {
  coverId: string | number
  coverLoaded: boolean
  thumbLoaded: boolean
  thumbLoading: boolean
  fileLoaded: boolean
  fileLoading: boolean
  currentLayer: 'cover' | 'thumb' | 'file'
}

export class StickerLoadingStateCacheManager extends SubCacheManager {

  /**
   * 获取贴纸加载状态
   */
  async getStickerLoadingState(stickerId: string | number): Promise<StickerLoadingState> {
    const key = stickerId.toString()
    const state = await this.store.getItem<StickerLoadingState>(key)

    if (!state) {
      const defaultState: StickerLoadingState = {
        coverId: stickerId,
        coverLoaded: false,
        thumbLoaded: false,
        thumbLoading: false,
        fileLoaded: false,
        fileLoading: false,
        currentLayer: 'cover'
      }
      await this.store.setItem(key, defaultState)
      return defaultState
    }

    return state
  }

  /**
   * 更新贴纸加载状态
   */
  async updateStickerLoadingState(
    stickerId: string | number,
    updates: Partial<StickerLoadingState>
  ): Promise<void> {
    const key = stickerId.toString()
    const currentState = await this.getStickerLoadingState(stickerId)
    const newState = { ...currentState, ...updates }
    await this.store.setItem(key, newState)
  }

  cleanup(_now: number, _maxAge: number) {
    return this.store.clear()
  }
}
