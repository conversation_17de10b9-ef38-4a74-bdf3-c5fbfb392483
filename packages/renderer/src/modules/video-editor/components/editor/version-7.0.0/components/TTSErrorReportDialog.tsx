import React from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, } from '@/components/ui/dialog.tsx'
import { Button } from '@/components/ui/button.tsx'
import { ScrollArea } from '@/components/ui/scroll-area.tsx'
import { AlertTriangle } from 'lucide-react'
import { TTSTaskStatus } from '@rve/editor/components/TextToSpeechProgressOverlay.tsx'

interface TTSErrorReportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  failedTasks: TTSTaskStatus[]
  successCount: number
  totalCount: number
}

/**
 * 文本转语音错误报告弹窗
 */
export function TTSErrorReportDialog({
  open,
  onOpenChange,
  failedTasks,
  successCount,
  totalCount
}: TTSErrorReportDialogProps) {
  const getStatusText = (status: TTSTaskStatus['status']) => {
    switch (status) {
      case 'FAILURE':
        return '生成失败'
      case 'REVOKED':
        return '任务取消'
      case 'TIMEOUT':
        return '任务超时'
      default:
        return '未知错误'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            <DialogTitle>语音生成完成</DialogTitle>
          </div>
          <DialogDescription>
            成功生成 {successCount} 个语音，{failedTasks.length} 个失败
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 成功统计 */}
          <div className="text-sm text-muted-foreground">
            总计 {totalCount} 个台词，成功 {successCount} 个，失败 {failedTasks.length} 个
          </div>

          {/* 失败列表 */}
          {failedTasks.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-2">失败详情：</h4>
              <ScrollArea className="h-32 border rounded-md p-2">
                <div className="space-y-2">
                  {failedTasks.map((task, index) => (
                    <div key={index} className="text-xs space-y-1">
                      <div className="font-medium truncate" title={task.text}>
                        {task.text}
                      </div>
                      <div className="text-muted-foreground">
                        {getStatusText(task.status)}
                        {task.error && `: ${task.error}`}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          <div className="flex justify-end">
            <Button onClick={() => onOpenChange(false)}>
              确定
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
