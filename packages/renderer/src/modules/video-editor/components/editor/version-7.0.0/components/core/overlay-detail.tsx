import React, { FC, useCallback, useMemo } from 'react'
import {
  OverlayEditingContext,
  OverlayEditingContextValues,
  useCachedOverlaysContext,
  useEditorContext
} from '@rve/editor/contexts'
import { Overlay, OverlayType } from '@app/rve-shared/types'
import { TextSetting } from '../overlays/text/text-setting'
import { GlobalSetting } from '../overlays/common/global-setting'
import { SingleOverlayUpdatePayload } from '../../contexts/editor/useOverlays'
import { merge } from 'lodash'
import { SoundSetting } from '../overlays/sounds/sound-setting'
import { VideoSetting } from '../overlays/video/video-setting'
import { ImageSetting } from '../overlays/images/image-setting'
import { CaptionSetting } from '../overlays/captions/caption-setting'

export const OverlayDetail: FC = () => {
  const { selectedOverlay, updateOverlay } = useEditorContext()
  const { overlays, requestUpdate } = useCachedOverlaysContext()

  const localOverlay = useMemo<Overlay | null>(
    () => overlays.find(o => o.id === selectedOverlay?.id) || null,
    [overlays, selectedOverlay]
  )

  const handleUpdateOverlay = useCallback<OverlayEditingContextValues['requestUpdate']>(
    (updater, commit = false) => {
      if (!localOverlay) return

      const updatedOverlay = (typeof updater === 'function') && localOverlay
        ? updater(localOverlay as any)
        : merge({}, localOverlay, updater) as SingleOverlayUpdatePayload

      requestUpdate(localOverlay.id, updatedOverlay)

      if (commit) {
        updateOverlay(localOverlay.id, () => updatedOverlay)
      }
    },
    [updateOverlay, localOverlay]
  )

  if (!localOverlay) {
    return (
      <div className="p-4 h-full overflow-y-auto">
        <GlobalSetting />
      </div>
    )
  }

  const overlayTypeComponentMap: Partial<Record<OverlayType, FC>> = {
    [OverlayType.TEXT]: TextSetting,
    [OverlayType.SOUND]: SoundSetting,
    [OverlayType.VIDEO]: VideoSetting,
    [OverlayType.CAPTION]: CaptionSetting,
    [OverlayType.STICKER]: ImageSetting,
  }

  const Component = overlayTypeComponentMap[localOverlay.type]

  return (
    <div className="p-4 h-full overflow-y-auto">
      <OverlayEditingContext
        value={{
          localOverlay,
          requestUpdate: handleUpdateOverlay
        }}
      >
        {Component && <Component />}
      </OverlayEditingContext>
    </div>
  )
}
