import React from 'react'
import { FormSlider, SectionTitle } from './form-components'
import { BaseOverlay, SoundOverlay } from '@app/rve-shared/types'
import { FPS } from '../../../constants'

/**
 * 淡入淡出控制组件的 Props 接口
 * 支持任何继承自 BaseOverlay 的类型
 */
interface FadeControlsProps<T extends BaseOverlay> {
  /**
   * 当前 overlay 对象
   */
  overlay: T

  /**
   * 更新 overlay 属性的回调函数
   */
  onOverlayChange: (updates: Partial<T>, commit?: boolean) => void

  /**
   * 淡入的最大时长限制（秒），默认为 10 秒
   */
  maxFadeInDuration?: number

  /**
   * 淡出的最大时长限制（秒），默认为 10 秒
   */
  maxFadeOutDuration?: number

  /**
   * 是否显示重置按钮，默认为 true
   */
  showResetButton?: boolean

  /**
   * 自定义样式类名
   */
  className?: string
}

/**
 * 淡入淡出控制组件
 *
 * 这是一个通用组件，可以用于任何支持淡入淡出的 overlay 类型（视频、音频等）
 *
 * 功能特性：
 * - 支持淡入时长调节（0-10秒，可配置）
 * - 支持淡出时长调节（0-10秒，可配置）
 * - 实时显示当前设置的时长
 * - 支持重置功能
 * - 类型安全的泛型设计
 * - 响应式设计，支持明暗主题
 *
 * @template T - 继承自 BaseOverlay 的 overlay 类型
 * @param props - 组件属性
 * @returns 淡入淡出控制 UI
 */
export function FadeControls<T extends BaseOverlay>({
  overlay,
  onOverlayChange,
  maxFadeInDuration = 10,
  maxFadeOutDuration = 10,
  showResetButton = true,
  className = ''
}: FadeControlsProps<T>) {
  // 获取当前淡入时长，默认为 0
  const fadeInDuration = overlay.fadeInDuration ?? 0

  // 获取当前淡出时长，默认为 0
  const fadeOutDuration = overlay.fadeOutDuration ?? 0

  // 计算 overlay 的总时长（秒）
  const totalDurationInSeconds = overlay.durationInFrames / FPS // 假设 30 FPS

  // 计算淡入淡出的最大允许时长
  // 允许淡入淡出各自最多占用总时长的 80%，这样即使两者都设置了最大值，也能保证视频中间部分有至少 20% 的时间是完全显示的
  const maxAllowedFadeIn = Math.min(maxFadeInDuration, totalDurationInSeconds * 0.8)
  const maxAllowedFadeOut = Math.min(maxFadeOutDuration, totalDurationInSeconds * 0.8)

  /**
   * 处理淡入时长变化
   */
  const handleFadeInChange = (value: number, commit?: boolean) => {
    // 只限制不超过最大允许值，不再考虑淡出时长

    const clampedValue = Math.max(0, Math.min(value, maxAllowedFadeIn))

    onOverlayChange({
      fadeInDuration: clampedValue,
      fadeOutDuration: overlay.fadeOutDuration
    } as Partial<T>, commit)
  }

  /**
   * 处理淡出时长变化
   */
  const handleFadeOutChange = (value: number, commit?: boolean) => {
    // 只限制不超过最大允许值，不再考虑淡入时长
    const clampedValue = Math.max(0, Math.min(value, maxAllowedFadeOut))
    onOverlayChange({
      fadeInDuration: overlay.fadeInDuration,
      fadeOutDuration: clampedValue
    } as Partial<T>, commit)
  }

  /**
   * 重置淡入淡出设置
   */
  const handleReset = () => {
    onOverlayChange({
      fadeInDuration: 0,
      fadeOutDuration: 0
    } as Partial<T>)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <SectionTitle
        title="淡入淡出"
        onReset={showResetButton ? handleReset : undefined}
      />

      {/* 淡入控制 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-700 dark:text-gray-300">淡入</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {fadeInDuration.toFixed(2)}秒
          </span>
        </div>
        <FormSlider
          value={fadeInDuration}
          onChange={(val, commit) => handleFadeInChange(val, commit)}
          min={0}
          max={maxAllowedFadeIn}
          step={0.1}
          showInput={false}
        />
      </div>

      {/* 淡出控制 */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-700 dark:text-gray-300">淡出</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {fadeOutDuration.toFixed(2)}秒
          </span>
        </div>
        <FormSlider
          value={fadeOutDuration}
          onChange={(val, commit) => handleFadeOutChange(val, commit)}
          min={0}
          max={maxAllowedFadeOut}
          step={0.1}
          showInput={false}
        />
      </div>

      {/* 提示信息 */}
      {(fadeInDuration + fadeOutDuration) > totalDurationInSeconds && (
        <div className="text-xs text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 p-2 rounded-md">
          注意：淡入淡出时长总和超过视频时长，中间部分可能完全透明
        </div>
      )}

      {/* 重叠提示 */}
      {fadeInDuration > 0 && fadeOutDuration > 0 && (fadeInDuration + fadeOutDuration) > totalDurationInSeconds && (
        <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded-md">
          淡入淡出时间重叠，将产生交叉淡化效果
        </div>
      )}
    </div>
  )
}

export const VideoFadeControls: React.FC<{
  overlay: BaseOverlay
  onOverlayChange: (updates: Partial<{ fadeInDuration?: number; fadeOutDuration?: number }>, commit?: boolean) => void
  className?: string
}> = ({ overlay, onOverlayChange, className }) => {
  return (
    <FadeControls
      overlay={overlay}
      onOverlayChange={(updates, commit) => onOverlayChange(updates, commit)}
      maxFadeInDuration={10}
      maxFadeOutDuration={10}
      className={className}
    />
  )
}

export const AudioFadeControls: React.FC<{
  overlay: SoundOverlay
  onOverlayChange: (updates: Partial<SoundOverlay>, commit?: boolean) => void
  className?: string
}> = ({ overlay, onOverlayChange, className }) => {
  return (
    <FadeControls
      overlay={overlay}
      onOverlayChange={onOverlayChange}
      maxFadeInDuration={5}
      maxFadeOutDuration={5}
      className={className}
    />
  )
}
