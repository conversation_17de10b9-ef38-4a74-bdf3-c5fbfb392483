
import React from 'react'
import { ColorSlider } from '@/components/ui/color-slider'
import { VideoOverlay } from '@app/rve-shared/types'
import { useOverlayEditing } from '@rve/editor/contexts'
import { FilterHelper } from '../../../../utils/filter-helper'

interface SectionTitleProps {
  children: React.ReactNode
  onReset?: () => void
}

export function AdjustSetting() {
  const { localOverlay: videoOverlay, requestUpdate: updateOverlay } = useOverlayEditing<VideoOverlay>()

  const currentFilter = videoOverlay?.styles?.filter || ''

  const getFilterValue = (filterName: string, defaultValue: number = 100) => {
    return FilterHelper.extractFilterValue(currentFilter, filterName, defaultValue)
  }

  const updateFilterValue = (filterName: string, value: number, unit: string = '%', commit?: boolean) => {
    const newFilter = FilterHelper.updateFilter(currentFilter, filterName, value, unit)
    updateOverlay({ styles: { filter: newFilter } }, commit)
  }

  const updateMultipleFilters = (updates: Array<{ filterName: string; value: number; unit?: string }>) => {
    const newFilter = FilterHelper.updateMultipleFilters(currentFilter, updates)
    updateOverlay({ styles: { filter: newFilter } }, true)
  }

  const resetAllFilters = () => {
    updateOverlay({ styles: { filter: '' } }, true)
  }

  const SectionTitle: React.FC<SectionTitleProps> = ({ children, onReset }) => (
    <div className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700">
      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{children}</span>
      {onReset && (
        <button
          onClick={onReset}
          className="text-xs text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 transition-colors"
        >
          重置
        </button>
      )}
    </div>
  )

  return (
    <div className="p-4 space-y-6 bg-background text-foreground">
      {/* 色彩调整 */}
      <div className="overlay-setting-card">
        <SectionTitle
          onReset={() => {
            updateMultipleFilters([
              { filterName: 'hue-rotate', value: 0, unit: 'deg' },
              { filterName: 'sepia', value: 0 },
              { filterName: 'saturate', value: 100 }
            ])
          }}
        >
          色彩
        </SectionTitle>

        <div className="space-y-4 mt-3">
          {/* 色温 - 使用 sepia 模拟冷暖效果 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>色温</span>
              <span>{getFilterValue('sepia', 0)}%</span>
            </div>
            <ColorSlider
              value={getFilterValue('sepia', 0)}
              onChange={(value, commit) => updateFilterValue('sepia', value, '%', commit)}
              trackClassName="bg-gradient-to-r from-blue-500 to-yellow-500"
              max={100}
              min={0}
            />
          </div>

          {/* 色调 - 使用 hue-rotate 完整色相环 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>色调</span>
              <span>{getFilterValue('hue-rotate', 0)}°</span>
            </div>
            <ColorSlider
              value={getFilterValue('hue-rotate', 0)}
              onChange={(value, commit) => updateFilterValue('hue-rotate', value, 'deg', commit)}
              trackClassName="bg-[linear-gradient(to_right,_#f00,_#ff0,_#0f0,_#0ff,_#00f,_#f0f,_#f00)]"
              max={360}
              min={0}
            />
          </div>

          {/* 饱和度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>饱和度</span>
              <span>{getFilterValue('saturate', 100)}%</span>
            </div>
            <ColorSlider
              value={getFilterValue('saturate', 100)}
              onChange={(value, commit) => updateFilterValue('saturate', value, '%', commit)}
              trackClassName="bg-gradient-to-r from-red-300 to-red-600"
              max={200}
              min={0}
              step={1}
            />
          </div>
        </div>
      </div>

      {/* 明度调整 */}
      <div className="overlay-setting-card">
        <SectionTitle
          onReset={() => {
            updateMultipleFilters([
              { filterName: 'brightness', value: 100 },
              { filterName: 'contrast', value: 100 }
            ])
          }}
        >
          明度
        </SectionTitle>

        <div className="space-y-4 mt-3">
          {/* 亮度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>亮度</span>
              <span>{getFilterValue('brightness', 100)}%</span>
            </div>
            <ColorSlider
              value={getFilterValue('brightness', 100)}
              onChange={(value, commit) => updateFilterValue('brightness', value, '%', commit)}
              trackClassName="bg-gradient-to-r from-black via-gray-500 to-white"
              max={200}
              min={0}
            />
          </div>

          {/* 对比度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>对比度</span>
              <span>{getFilterValue('contrast', 100)}%</span>
            </div>
            <ColorSlider
              value={getFilterValue('contrast', 100)}
              onChange={(value, commit) => updateFilterValue('contrast', value, '%', commit)}
              trackClassName="bg-gradient-to-r from-gray-500 to-white"
              max={200}
              min={0}
            />
          </div>
        </div>
      </div>

      {/* 效果调整 */}
      <div className="overlay-setting-card">
        <SectionTitle
          onReset={() => {
            updateMultipleFilters([
              { filterName: 'blur', value: 0, unit: 'px' },
              { filterName: 'contrast', value: 100 },
              { filterName: 'grayscale', value: 0 },
              { filterName: 'sepia', value: 0 }
            ])
          }}
        >
          效果
        </SectionTitle>

        <div className="space-y-4 mt-3">
          {/* 锐化 - 使用 blur + contrast 组合模拟 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>锐化</span>
              <span>{Math.round((getFilterValue('contrast', 100) - 100) / 2)}%</span>
            </div>
            <ColorSlider
              value={Math.round((getFilterValue('contrast', 100) - 100) / 2)}
              onChange={(value, commit) => {
                // 锐化效果：增加对比度，减少轻微模糊
                const contrastValue = 100 + value * 2 // 0-100% 映射到 100-300% 对比度
                const blurValue = Math.max(0, 1 - value / 50) // 轻微反向模糊，最大1px

                if (commit) {
                  updateMultipleFilters([
                    { filterName: 'contrast', value: contrastValue },
                    { filterName: 'blur', value: blurValue, unit: 'px' }
                  ])
                } else {
                  // 实时预览时也应用相同的滤镜组合
                  const newFilter = FilterHelper.updateMultipleFilters(currentFilter, [
                    { filterName: 'contrast', value: contrastValue },
                    { filterName: 'blur', value: blurValue, unit: 'px' }
                  ])
                  updateOverlay({ styles: { filter: newFilter } }, false)
                }
              }}
              trackClassName="bg-gradient-to-r from-gray-400 to-white"
              max={100}
              min={0}
            />
          </div>

          {/* 灰度 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>灰度</span>
              <span>{getFilterValue('grayscale', 0)}%</span>
            </div>
            <ColorSlider
              value={getFilterValue('grayscale', 0)}
              onChange={(value, commit) => updateFilterValue('grayscale', value, '%', commit)}
              trackClassName="bg-gradient-to-r from-white to-gray-500"
              max={100}
              min={0}
            />
          </div>

          {/* 复古 */}
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>复古</span>
              <span>{getFilterValue('sepia', 0)}%</span>
            </div>
            <ColorSlider
              value={getFilterValue('sepia', 0)}
              onChange={(value, commit) => updateFilterValue('sepia', value, '%', commit)}
              trackClassName="bg-gradient-to-r from-white to-yellow-700"
              max={100}
              min={0}
            />
          </div>
        </div>
      </div>

      {/* 重置所有滤镜按钮 */}
      <div className="flex justify-end">
        <button
          onClick={resetAllFilters}
          className="px-3 py-1.5 text-sm bg-primary/10 text-primary hover:bg-primary/20 rounded-md transition-colors"
        >
          重置所有滤镜
        </button>
      </div>
    </div>
  )
}

