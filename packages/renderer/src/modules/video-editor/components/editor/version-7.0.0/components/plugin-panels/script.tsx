import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ScriptSceneData, useQueryScript } from '@/hooks/queries/useQueryScript.ts'
import { useEditorContext } from '@rve/editor/contexts'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Download, Edit, Mic2 } from 'lucide-react'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore.ts'
import { LoadingIndicator } from '@/components/LoadingIndicator.tsx'
import { estimateSentencesDuration, extractSentencesFromScriptScene } from '@/libs/tools/script.ts'
import { OverlayType, SoundOverlay, StoryboardOverlay, TextOverlay, Track, TrackType } from '@app/rve-shared/types'
import { generateNewOverlayId } from '@rve/editor/utils/track-helper.ts'
import {
  DEFAULT_OVERLAY,
  DEFAULT_TEXT_FONT_SRC,
  DEFAULT_TEXT_OVERLAY_STYLES,
  FPS,
  TEXT_TO_SPEECH_CONFIG
} from '@rve/editor/constants'
import { EVENT_NAMES, useEventListener } from '@rve/editor/hooks/useEventBus'
import { cn } from '@/components/lib/utils'
import { AiModule } from '@/libs/request/api/ai'
import {
  TextToSpeechProgressOverlay,
  TTSProgressState,
  TTSTaskStatus
} from '@rve/editor/components/TextToSpeechProgressOverlay.tsx'
import { TTSErrorReportDialog } from '@rve/editor/components/TTSErrorReportDialog.tsx'
import { toast } from 'react-toastify'

// 脚本段落组件
export const ScriptScene: React.FC<{
  scene: ScriptSceneData
  index: number
}> = ({ scene, index }) => {
  // 将脚本内容按行分割成句子
  const sentences = useMemo(() => extractSentencesFromScriptScene(scene), [scene])

  const [minDuration, maxDuration] = estimateSentencesDuration(sentences)

  return (
    <div className="mb-6">
      {/* 段落标题 */}
      <div className="flex items-center gap-2 mb-3">
        <h3 className="text-md font-medium">
          #{index + 1} {scene.title || '未命名分镜'}
        </h3>
        {minDuration && (
          <div className="text-xs text-gray-500">
            预计时长 {minDuration}s {maxDuration && `~ ${maxDuration}s`}
          </div>
        )}
      </div>

      {/* 句子列表 */}
      <SentenceList sentences={sentences} />

      {/* 画面说明 */}
      {scene.notes && (
        <div className="mt-3">
          <div className="text-xs font-medium text-gray-700 mb-1">画面说明</div>
          <div className="text-xs text-gray-600  p-2 rounded">
            {scene.notes}
          </div>
        </div>
      )}

      {/* 拍摄示例 */}
      {/*<div className="mt-3">*/}
      {/*  <div className="text-xs font-medium text-gray-700 mb-1">拍摄示例  </div>*/}
      {/*  <div className="text-xs text-gray-600" />*/}
      {/*</div>*/}
    </div>
  )
}

// 句子列表组件
export const SentenceList: React.FC<{
  sentences: string[]
}> = ({ sentences }) => {
  return (
    <div className="space-y-1">
      {sentences.map((sentence, index) => (
        <div key={index} className="flex items-start gap-2 text-xs">
          <span className="text-gray-400 text-xs mt-0.5 min-w-[20px]">
            {index + 1}.
          </span>
          <span className="text-gray-400 leading-relaxed">
            {sentence}
          </span>
        </div>
      ))}
    </div>
  )
}

/**
 * 计算句子的时长（帧数）
 */
const calculateSentenceDurationInFrames = (sentence: string): number => {
  // 简单预估10个字符为 1 秒，转换为帧数
  const durationInSeconds = sentence.length / 10
  return Math.max(FPS * durationInSeconds, FPS) // 最少1秒
}

/**
 * 生成分镜轨道和口播轨道
 */
const generateTracksFromScript = (
  scenes: ScriptSceneData[],
  existingTracks: Track[],
  width: number,
  height: number
): Track[] => {
  const MINIMUM_DURATION = FPS

  if (!scenes.length) return []

  // 获取下一个可用的 overlay ID
  let nextOverlayId = generateNewOverlayId(existingTracks)

  // 1. 生成分镜轨道
  const storyboardTrack: Track = {
    type: TrackType.STORYBOARD,
    overlays: []
  }

  let currentFrame = 0

  // 为每个 scene 创建分镜 overlay
  scenes.forEach(scene => {
    const sentences = extractSentencesFromScriptScene(scene)

    // 计算该分镜的时长：取所有句子预估时长的最大值
    const [minDuration, maxDuration = minDuration] = estimateSentencesDuration(sentences)
    const sceneDurationInFrames = maxDuration
      ? Math.max(maxDuration * FPS, MINIMUM_DURATION)
      : MINIMUM_DURATION

    const storyboardOverlay: StoryboardOverlay = {
      ...DEFAULT_OVERLAY,
      id: nextOverlayId++,
      type: OverlayType.STORYBOARD,
      from: currentFrame,
      durationInFrames: sceneDurationInFrames,
    }

    storyboardTrack.overlays.push(storyboardOverlay)
    currentFrame += sceneDurationInFrames
  })

  // 2. 计算需要的口播轨道数量（所有 scenes 中的最大句子数量）
  const sentenceCounts = scenes.map(scene =>
    extractSentencesFromScriptScene(scene).length
  )
  const maxSentenceCount = sentenceCounts.length > 0 ? Math.max(...sentenceCounts) : 0

  // 3. 生成口播轨道
  const narrationTracks: Track[] = []

  for (let trackIndex = 0; trackIndex < maxSentenceCount; trackIndex++) {
    const narrationTrack: Track = {
      type: TrackType.NARRATION,
      overlays: []
    }

    let sceneStartFrame = 0

    // 为每个 scene 在当前口播轨道中添加对应的句子
    scenes.forEach((scene, sceneIndex) => {
      const sentences = extractSentencesFromScriptScene(scene)
      const sentence = sentences[trackIndex] // 第 trackIndex 个句子

      if (sentence && sentence.trim()) {
        // 计算句子时长
        const sentenceDurationInFrames = calculateSentenceDurationInFrames(sentence)

        const textOverlay: TextOverlay = {
          ...DEFAULT_OVERLAY,
          id: nextOverlayId++,
          type: OverlayType.TEXT,
          content: sentence.trim(),
          src: DEFAULT_TEXT_FONT_SRC,
          from: sceneStartFrame,
          durationInFrames: sentenceDurationInFrames,
          width: width * 0.8,
          height: 100,
          left: width * 0.1,
          top: height * 0.8,
          storyboardIndex: sceneIndex, // 关联到对应的分镜
          styles: {
            ...DEFAULT_TEXT_OVERLAY_STYLES,
            textAlign: 'center',
          }
        }

        narrationTrack.overlays.push(textOverlay)
      }

      // 更新下一个分镜的起始帧位置
      const storyboardOverlay = storyboardTrack.overlays[sceneIndex]
      if (storyboardOverlay) {
        sceneStartFrame += storyboardOverlay.durationInFrames
      }
    })

    narrationTracks.push(narrationTrack)
  }

  return [storyboardTrack, ...narrationTracks]
}

const ScriptPanel = () => {
  const { scriptId, tracks, updateTracks, getAspectRatioDimensions } = useEditorContext()
  const { data: script } = useQueryScript(scriptId)
  const { pushNamedTab } = useVirtualTabsStore()

  // 高光特效状态
  const [isHighlighted, setIsHighlighted] = useState(false)

  // 文本转语音状态
  const [ttsProgressState, setTtsProgressState] = useState<TTSProgressState>({
    visible: false,
    completed: 0,
    total: 0,
    tasks: []
  })

  // 错误报告弹窗状态
  const [errorReportOpen, setErrorReportOpen] = useState(false)
  const [failedTasks, setFailedTasks] = useState<TTSTaskStatus[]>([])

  // 过滤出有内容的场景
  const scenes = useMemo(() => {
    return script?.content || []
  }, [script])

  // 监听高光事件
  useEventListener(EVENT_NAMES.HIGHLIGHT_SCRIPT_BUTTONS, () => {
    setIsHighlighted(true)

    const fadeOutTimer = setTimeout(() => {
      setIsHighlighted(false)
    }, 2000)

    return () => clearTimeout(fadeOutTimer)
  })

  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      setIsHighlighted(false)
    }
  }, [])

  // 高光特效样式类
  const highlightClasses = cn(
    'text-xs h-7 px-3 relative z-10 transition-all duration-600',
    isHighlighted && `
      bg-gradient-brand text-white border-transparent shadow-xl hover:opacity-90
      opacity-100 scale-100 animate-pulse ring-4 ring-[var(--color-primary-highlight)]/30
    `
  )

  // 高光容器样式（用于外层包装）
  const highlightContainerClasses = isHighlighted
    ? cn(
      'relative transition-all duration-600',
      'before:absolute before:-inset-1 before:bg-gradient-brand before:rounded-md before:opacity-20 before:animate-ping before:pointer-events-none'
    )
    : ''

  // 处理按钮点击时取消高光
  const handleButtonClick = (originalHandler?: () => void) => {
    if (isHighlighted) {
      setTimeout(() => {
        setIsHighlighted(false)
        originalHandler?.()
      }, 300) // 快速淡出
    } else {
      // 如果没有高光或已经在淡出，直接执行
      originalHandler?.()
    }
  }

  const importTextOnly = useCallback(() => {
    if (!scenes.length) {
      console.warn('没有可导入的脚本内容')
      return
    }

    const { width, height } = getAspectRatioDimensions()
    // 生成新的分镜轨道和口播轨道
    const newTracks = generateTracksFromScript(scenes, tracks, width, height)

    if (!newTracks.length) {
      console.warn('生成的轨道为空')
      return
    }

    // 替换现有的分镜轨道和口播轨道，保留其他类型轨道
    updateTracks(prevTracks => {
      const filteredTracks = prevTracks.filter(track =>
        track.type !== TrackType.STORYBOARD && track.type !== TrackType.NARRATION
      )

      return [...newTracks, ...filteredTracks]
    })
  }, [scenes, tracks, updateTracks, getAspectRatioDimensions])

  /**
   * 朗读全部台词功能
   */
  const readAllDialogues = useCallback(async () => {
    if (!scenes.length) {
      toast.warning('没有可朗读的脚本内容')
      return
    }

    // 提取所有台词
    const allSentences: { text: string; sceneIndex: number; sentenceIndex: number }[] = []
    scenes.forEach((scene, sceneIndex) => {
      const sentences = extractSentencesFromScriptScene(scene)
      sentences.forEach((sentence, sentenceIndex) => {
        if (sentence.trim()) {
          allSentences.push({ text: sentence.trim(), sceneIndex, sentenceIndex })
        }
      })
    })

    if (!allSentences.length) {
      toast.warning('没有找到有效的台词内容')
      return
    }

    // 初始化进度状态 - 简化为基于句子数量的进度计算
    setTtsProgressState({
      visible: true,
      completed: 0,
      total: allSentences.length,
      tasks: []
    })

    const tasks: TTSTaskStatus[] = []
    const completedTasks: TTSTaskStatus[] = []

    try {
      // 第一步：为所有台词创建TTS任务
      for (let i = 0; i < allSentences.length; i++) {
        const sentence = allSentences[i]

        try {
          const response = await AiModule.endpoints.textToSpeech({
            text: sentence.text,
            ...TEXT_TO_SPEECH_CONFIG.defaultParams
          })

          const task: TTSTaskStatus = {
            taskId: response.task_id,
            text: sentence.text,
            status: 'PENDING'
          }

          tasks.push(task)
        } catch (error) {
          console.error('创建TTS任务失败:', error)
          const failedTask: TTSTaskStatus = {
            taskId: '',
            text: sentence.text,
            status: 'FAILURE',
            error: error instanceof Error ? error.message : '创建任务失败'
          }
          tasks.push(failedTask)
        }
      }

      setTtsProgressState(prev => ({
        ...prev,
        tasks: [...tasks]
      }))

      // 第二步：轮询任务状态
      const pollTasks = async () => {
        const pendingTasks = tasks.filter(task => task.status === 'PENDING')

        if (pendingTasks.length === 0) {
          return true // 所有任务完成
        }

        for (const task of pendingTasks) {
          try {
            const statusResponse = await AiModule.endpoints.queryTaskStatus(task.taskId)

            if (statusResponse.status === 'SUCCESS') {
              task.status = 'SUCCESS'
              task.audioUrl = statusResponse.result?.data?.video_name
              completedTasks.push(task)
            } else if (statusResponse.status === 'FAILURE' || statusResponse.status === 'REVOKED') {
              task.status = statusResponse.status
              task.error = '任务执行失败'
              completedTasks.push(task)
            }
          } catch (error) {
            console.error('查询任务状态失败:', error)
            // 继续轮询，不标记为失败
          }
        }

        // TTS任务完成不更新进度，因为还需要进行音频缓存和时长提取

        return completedTasks.length >= allSentences.length
      }

      // 开始轮询
      const startTime = Date.now()
      const pollInterval = setInterval(async () => {
        const isComplete = await pollTasks()

        if (isComplete) {
          clearInterval(pollInterval)

          // 处理超时任务
          const timeoutTasks = tasks.filter(task =>
            task.status === 'PENDING' &&
            Date.now() - startTime > TEXT_TO_SPEECH_CONFIG.taskTimeout
          )

          timeoutTasks.forEach(task => {
            task.status = 'TIMEOUT'
            task.error = '任务超时'
          })

          // 生成轨道
          await generateTracksWithAudio(allSentences, tasks)
        } else if (Date.now() - startTime > TEXT_TO_SPEECH_CONFIG.taskTimeout) {
          clearInterval(pollInterval)

          // 标记剩余任务为超时
          tasks.forEach(task => {
            if (task.status === 'PENDING') {
              task.status = 'TIMEOUT'
              task.error = '任务超时'
            }
          })

          // 生成轨道
          await generateTracksWithAudio(allSentences, tasks)
        }
      }, TEXT_TO_SPEECH_CONFIG.pollInterval)
    } catch (error) {
      console.error('朗读全部台词失败:', error)
      toast.error('朗读功能执行失败，请重试')

      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })
    }
  }, [scenes])

  /**
   * 使用音频URL生成轨道 - 重构版本
   * 工作流程：音频优先 → 时长提取 → 轨道生成
   */
  const generateTracksWithAudio = useCallback(async (
    allSentences: { text: string; sceneIndex: number; sentenceIndex: number }[],
    tasks: TTSTaskStatus[]
  ) => {
    const { width, height } = getAspectRatioDimensions()

    try {
      // 第一步：筛选成功和失败的TTS任务
      const successTasks = tasks.filter(task => task.status === 'SUCCESS' && task.audioUrl)
      const failedTasks = tasks.filter(task => task.status !== 'SUCCESS')

      // 第二步：逐个处理句子的音频缓存和时长提取，每完成一个句子更新进度
      const audioInfoMap = new Map<string, { durationInFrames: number; localUrl: string; error?: string }>()
      let completedSentences = 0

      // 首先处理失败的任务，直接标记为完成（使用默认时长）
      for (const failedTask of failedTasks) {
        const estimatedDurationSeconds = Math.max(1, failedTask.text.length / 10)
        const estimatedDurationInFrames = Math.round(estimatedDurationSeconds * FPS)

        audioInfoMap.set(failedTask.text, {
          durationInFrames: estimatedDurationInFrames,
          localUrl: '', // 失败的任务没有音频URL
          error: failedTask.error || 'TTS任务失败'
        })

        // 失败的句子也算作完成处理，更新进度
        completedSentences++
        setTtsProgressState(prev => ({
          ...prev,
          completed: completedSentences
        }))

        console.log(`句子处理完成（TTS失败，使用默认时长） (${completedSentences}/${allSentences.length}): ${failedTask.text}`)
      }

      // 然后处理成功的任务，进行音频缓存和时长提取
      for (let i = 0; i < successTasks.length; i++) {
        const task = successTasks[i]

        try {
          const { duration, localUrl } = await window.editor.getAudioDuration(task.audioUrl!)
          const audioDurationInFrames = Math.round(duration * FPS)

          audioInfoMap.set(task.text, {
            durationInFrames: audioDurationInFrames,
            localUrl
          })

          // 句子完整处理完成，更新进度
          completedSentences++
          setTtsProgressState(prev => ({
            ...prev,
            completed: completedSentences
          }))

          console.log(`句子处理完成 (${completedSentences}/${allSentences.length}): ${task.text} -> ${duration}秒`)
        } catch (error) {
          console.error(`获取音频时长失败: ${task.text}`, error)

          // 使用估算时长作为备选
          const estimatedDurationSeconds = Math.max(1, task.text.length / 10)
          const estimatedDurationInFrames = Math.round(estimatedDurationSeconds * FPS)

          audioInfoMap.set(task.text, {
            durationInFrames: estimatedDurationInFrames,
            localUrl: task.audioUrl!, // 使用原始URL作为备选
            error: error instanceof Error ? error.message : '获取时长失败'
          })

          // 即使失败也算作完成处理，更新进度
          completedSentences++
          setTtsProgressState(prev => ({
            ...prev,
            completed: completedSentences
          }))

          console.log(`句子处理完成（使用估算时长） (${completedSentences}/${allSentences.length}): ${task.text}`)
        }
      }

      // 第三步：基于真实音频时长生成轨道
      const newTracks = generateTracksFromAudioInfo(
        allSentences,
        successTasks,
        audioInfoMap,
        width,
        height
      )

      // 更新轨道
      updateTracks(prevTracks => {
        const filteredTracks = prevTracks.filter(track =>
          track.type !== TrackType.STORYBOARD &&
          track.type !== TrackType.NARRATION
        )

        return [...newTracks, ...filteredTracks]
      })

      // 隐藏进度遮罩
      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })

      // 显示结果
      const audioErrorCount = Array.from(audioInfoMap.values()).filter(info => info.error).length

      if (failedTasks.length > 0) {
        setFailedTasks(failedTasks)
        setErrorReportOpen(true)
      } else if (audioErrorCount > 0) {
        toast.warning(`成功生成 ${successTasks.length} 个语音轨道，其中 ${audioErrorCount} 个使用了估算时长`)
      } else {
        toast.success(`成功生成 ${successTasks.length} 个语音轨道`)
      }
    } catch (error) {
      console.error('生成音频轨道失败:', error)
      toast.error('生成音频轨道失败，请重试')

      setTtsProgressState({
        visible: false,
        completed: 0,
        total: 0,
        tasks: []
      })
    }
  }, [scenes, tracks, updateTracks, getAspectRatioDimensions])

  /**
   * 基于音频信息生成轨道
   */
  const generateTracksFromAudioInfo = useCallback(
    (
      allSentences: { text: string; sceneIndex: number; sentenceIndex: number }[],
      successTasks:  TTSTaskStatus[],
      audioInfoMap: Map<string, { durationInFrames: number; localUrl: string; error?: string }>,
      width: number,
      height: number
    ): Track[] => {
      const MINIMUM_DURATION = FPS
      let nextOverlayId = generateNewOverlayId(tracks)

      // 按场景组织句子和音频信息
      const sceneData = new Map<number, {
        sentences: { text: string; sentenceIndex: number; audioInfo?: { durationInFrames: number; originalUrl?: string; localUrl: string } }[]
        totalDuration: number
      }>()

      // 初始化场景数据
      scenes.forEach((_, sceneIndex) => {
        sceneData.set(sceneIndex, { sentences: [], totalDuration: 0 })
      })

      // 填充句子和音频信息
      allSentences.forEach(sentence => {
        const sceneInfo = sceneData.get(sentence.sceneIndex)
        if (!sceneInfo) return

        const task = successTasks.find(t => t.text === sentence.text)
        const audioInfo = audioInfoMap.get(sentence.text)

        const sentenceData = {
          text: sentence.text,
          sentenceIndex: sentence.sentenceIndex,
          audioInfo: audioInfo ? {
            durationInFrames: audioInfo.durationInFrames,
            originalUrl: task?.audioUrl, // 原始远程URL（可能为空，如果TTS失败）
            localUrl: audioInfo.localUrl  // 本地缓存URL
          } : undefined
        }

        sceneInfo.sentences.push(sentenceData)

        // 计算场景总时长（取最长的句子时长）
        if (audioInfo) {
          sceneInfo.totalDuration = Math.max(sceneInfo.totalDuration, audioInfo.durationInFrames)
        }
      })

      // 确保每个场景至少有最小时长
      sceneData.forEach(sceneInfo => {
        if (sceneInfo.totalDuration < MINIMUM_DURATION) {
          sceneInfo.totalDuration = MINIMUM_DURATION
        }
      })

      // 生成分镜轨道
      const storyboardTrack: Track = {
        type: TrackType.STORYBOARD,
        overlays: []
      }

      let currentFrame = 0
      scenes.forEach((_, sceneIndex) => {
        const sceneInfo = sceneData.get(sceneIndex)
        if (!sceneInfo) return

        const storyboardOverlay: StoryboardOverlay = {
          ...DEFAULT_OVERLAY,
          id: nextOverlayId++,
          type: OverlayType.STORYBOARD,
          from: currentFrame,
          durationInFrames: sceneInfo.totalDuration,
        }

        storyboardTrack.overlays.push(storyboardOverlay)
        currentFrame += sceneInfo.totalDuration
      })

      // 计算需要的口播轨道数量
      const maxSentenceCount = Math.max(...Array.from(sceneData.values()).map(info => info.sentences.length))

      // 生成口播轨道
      const narrationTracks: Track[] = []

      for (let trackIndex = 0; trackIndex < maxSentenceCount; trackIndex++) {
        const narrationTrack: Track = {
          type: TrackType.NARRATION,
          overlays: []
        }

        let sceneStartFrame = 0

        scenes.forEach((_, sceneIndex) => {
          const sceneInfo = sceneData.get(sceneIndex)
          if (!sceneInfo) return

          const sentence = sceneInfo.sentences[trackIndex]
          if (!sentence || !sentence.text.trim()) {
          // 更新下一个分镜的起始帧位置
            sceneStartFrame += sceneInfo.totalDuration
            return
          }

          // 创建TextOverlay
          const textDuration = sentence.audioInfo?.durationInFrames || Math.max(FPS, sentence.text.length / 10 * FPS)

          const textOverlay: TextOverlay = {
            ...DEFAULT_OVERLAY,
            id: nextOverlayId++,
            type: OverlayType.TEXT,
            content: sentence.text.trim(),
            src: DEFAULT_TEXT_FONT_SRC,
            from: sceneStartFrame,
            durationInFrames: textDuration,
            width: width * 0.8,
            height: 100,
            left: width * 0.1,
            top: height * 0.8,
            storyboardIndex: sceneIndex,
            styles: {
              ...DEFAULT_TEXT_OVERLAY_STYLES,
              textAlign: 'center',
            }
          }

          narrationTrack.overlays.push(textOverlay)

          // 如果有音频且TTS成功（有原始URL），创建对应的SoundOverlay
          if (sentence.audioInfo && sentence.audioInfo.originalUrl) {
            const soundOverlay: SoundOverlay = {
              id: nextOverlayId++,
              type: OverlayType.SOUND,
              content: sentence.audioInfo.originalUrl, // 使用原始远程URL
              src: sentence.audioInfo.originalUrl,     // 使用原始远程URL
              localSrc: sentence.audioInfo.localUrl,   // 使用本地缓存URL
              durationInFrames: sentence.audioInfo.durationInFrames,
              from: sceneStartFrame,
              height: 100,
              width: 200,
              left: 0,
              top: 0,
              isDragging: false,
              rotation: 0,
              storyboardIndex: sceneIndex,
              styles: {
                volume: 1,
              },
            }

            narrationTrack.overlays.push(soundOverlay)
          }

          // 更新下一个分镜的起始帧位置
          sceneStartFrame += sceneInfo.totalDuration
        })

        narrationTracks.push(narrationTrack)
      }

      return [storyboardTrack, ...narrationTracks]
    },
    [scenes, tracks]
  )

  if (!script) return <LoadingIndicator />

  return (
    <div className="h-full flex flex-col w-full">
      {/* 顶部工具栏 */}
      <div className="flex items-center p-4 border-b border-gray-200">
        <div className="flex-grow">
          {script.title}
        </div>

        <Button
          variant="ghost"
          size="sm"
          className="text-xs h-7 px-3 text-gray-400"
          onClick={() => pushNamedTab('Script', { id: script.id.toString() })}
        >
          <Edit className="w-3 h-3 mr-1" />
          编辑脚本
        </Button>

        <div className={cn('flex gap-1', highlightContainerClasses)}>
          <div className={cn('relative', )}>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                highlightClasses
              )}
              onClick={() => handleButtonClick(importTextOnly)}
            >
              <Download className="w-3 h-3 mr-1" />
              仅导入台词
            </Button>
          </div>

          <div className={cn('relative')}>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                highlightClasses
              )}
              onClick={() => handleButtonClick(readAllDialogues)}
              disabled={ttsProgressState.visible}
            >
              <Mic2 className="w-3 h-3 mr-1" />
              朗读全部台词
            </Button>
          </div>
        </div>

      </div>

      {/* 脚本内容区域 */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {scenes.length > 0 ? (
            scenes.map((scene, index) => (
              <ScriptScene
                key={scene.id}
                scene={scene}
                index={index}
              />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center h-40 text-gray-500">
              <div className="text-sm">暂无脚本内容</div>
              <div className="text-xs mt-1">请先添加脚本内容</div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* 文本转语音进度遮罩 */}
      <TextToSpeechProgressOverlay progressState={ttsProgressState} />

      {/* 错误报告弹窗 */}
      <TTSErrorReportDialog
        open={errorReportOpen}
        onOpenChange={setErrorReportOpen}
        failedTasks={failedTasks}
        successCount={ttsProgressState.total - failedTasks.length}
        totalCount={ttsProgressState.total}
      />

    </div>
  )
}

export default React.memo(ScriptPanel)
