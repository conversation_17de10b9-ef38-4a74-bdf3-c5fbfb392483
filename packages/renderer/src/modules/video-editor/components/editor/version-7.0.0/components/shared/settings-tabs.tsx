import React, { ReactNode } from 'react'
import {
  Tabs,
  Ta<PERSON><PERSON>ontent,
  Ta<PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs'
import { LucideIcon } from 'lucide-react'

export interface TabItem {
  value: string
  label: string
  icon?: LucideIcon
  content: ReactNode
}

interface SettingsTabsProps {
  tabs: TabItem[]
  defaultTab?: string
  className?: string
  onTabChange?: (value: string) => void
}

/**
 * 通用设置选项卡组件
 * 用于显示设置和样式等选项卡面板
 */
export function SettingsTabs({
  tabs,
  defaultTab,
  className = 'w-full',
  onTabChange
}: SettingsTabsProps) {
  return (
    <Tabs defaultValue={defaultTab || tabs[0]?.value} onValueChange={onTabChange} className={className}>
      <TabsList className="w-full flex gap-3 bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-sm border border-gray-200 dark:border-gray-700">
        {tabs.map(tab => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-gray-900 dark:data-[state=active]:text-white
            rounded-sm flex-1 transition-all duration-200 text-gray-600 dark:text-zinc-400 hover:text-gray-900 dark:hover:text-zinc-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50"
          >
            <span className="flex items-center gap-2 text-xs">
              {tab.icon && React.createElement(tab.icon, { className: 'w-3 h-3' })}
              {tab.label}
            </span>
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map(tab => (
        <TabsContent key={tab.value} value={tab.value} className="space-y-4 mt-4 h-auto">
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  )
} 