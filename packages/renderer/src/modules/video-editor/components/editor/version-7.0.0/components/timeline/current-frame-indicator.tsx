import React from 'react'
import { PIXELS_PER_FRAME } from '../../constants'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'

/**
 * TimelineMarker component displays a marker on a timeline to indicate the current position.
 * It renders a vertical line with a triangle pointer at the top.
 *
 * @component
 */
export const CurrentFrameIndicator: React.FC = () => {
  const { videoPlayer: { currentFrame } } = useEditorContext()
  const { zoomScale } = useTimeline()

  return (
    <div
      id="CurrentFrameIndicator"
      className="absolute top-0 w-[2px] bg-red-500/90 dark:bg-red-500 pointer-events-none z-50"
      style={{
        left: currentFrame * zoomScale * PIXELS_PER_FRAME,
        // transform: 'translateX(-50%)',
        height: 'calc(100% + 0px)',
        top: '0px',
        willChange: 'transform, left',
      }}
    >
      {/* Triangle pointer at the top of the marker */}
      <div
        className="w-0 h-0 absolute top-[0px] left-1/2 transform -translate-x-1/2
            border-l-[5px] border-r-[5px] border-t-[8px]
            border-l-transparent border-r-transparent
            border-t-red-500/90 dark:border-t-red-500"
        style={{ willChange: 'transform' }}
      />
    </div>
  )
}
