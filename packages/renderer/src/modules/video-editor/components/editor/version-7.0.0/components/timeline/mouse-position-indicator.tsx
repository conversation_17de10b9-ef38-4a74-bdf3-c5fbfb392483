import React from 'react'
import { useTimeline } from '@rve/editor/contexts'
import { PIXELS_PER_FRAME } from '@rve/editor/constants'

/**
 * GhostMarker 组件显示一条顶部带有三角形的垂直线，以指示特定位置。
 * 该组件被用于在编辑界面中显示当前鼠标指向的时间点
 */
export const MousePositionIndicator: React.FC = () => {
  const { isContextMenuOpen, isDragging, mouseOnCurrentFrame, zoomScale } = useTimeline()

  // Don't render the marker on mobile, when position is not set, or during dragging
  if (mouseOnCurrentFrame === null || isDragging || isContextMenuOpen) {
    return null
  }

  return (
    <div
      className="absolute top-0 w-0 border-l border-r border-0.5 border-sky-500/50 dark:border-blue-500/50 border-dashed pointer-events-none z-40"
      style={{
        left: mouseOnCurrentFrame * PIXELS_PER_FRAME * zoomScale,
        height: 'calc(100%)',
      }}
    >
      <div className="w-0 h-0 border-l-[5px] border-r-[5px] border-t-[8px] border-l-transparent border-r-transparent border-t-sky-500 dark:border-t-blue-500 absolute top-[0px] left-1/2 transform -translate-x-1/2 pointer-events-none" />
    </div>
  )
}

