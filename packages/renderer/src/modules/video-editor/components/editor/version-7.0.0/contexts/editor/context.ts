import { createContext, useContext } from 'react'
import { HistoryHook } from './useHistory'
import { OverlaysHook } from './useOverlays'
import { VideoPlayerHook } from './useVideoPlayer'
import { AspectRatioHook } from './useAspectRatio'
import { CompositionDuration } from './useCompositionDuration'

export interface EditorContextValues extends OverlaysHook,
  AspectRatioHook,
  Pick<CompositionDuration, 'durationInFrames' | 'durationInSeconds'>
{
  scriptId: string
  projectId: string

  videoPlayer: VideoPlayerHook
  history: HistoryHook

  /**
   * Manual save project
   */
  saveProject(): Promise<void>

  /**
   * Trigger media rendering
   * @deprecated
   */
  renderMedia: () => void

  /**
   * General state object with proper typing
   * @deprecated
   */
  state: any
}

export const EditorContext = createContext<EditorContextValues>({} as any)

export const useEditorContext = (): EditorContextValues => {
  const context = useContext(EditorContext)
  if (!context) {
    throw new Error('useEditorContext must be used within an EditorProvider')
  }
  return context
}
