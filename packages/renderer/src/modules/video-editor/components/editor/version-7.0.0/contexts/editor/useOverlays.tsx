import React, { useCallback, useMemo, useState } from 'react'
import { Overlay, OverlayType, Track, TrackType } from '@app/rve-shared/types'
import { cloneDeep, merge } from 'lodash'
import {
  findOverlay,
  findOverlayStoryboard,
  findStoryboardIndex,
  getOverlayTrackIndex
} from '@rve/editor/utils/overlay-helper.ts'
import { getOverlayTimeRange } from '@app/rve-shared/utils'
import { ensureEmptyTracksAndSort } from '@rve/editor/utils/track-helper.ts'

export type SingleOverlayUpdatePayload = Overlay & { targetTrackIndex?: number }

export type OverlaysHook = {
  tracks: Track[]
  setTracksDirectly: React.Dispatch<React.SetStateAction<Track[]>>
  updateTracks: React.Dispatch<React.SetStateAction<Track[]>>

  // 编辑器中所有 overlay 的数组
  overlays: Overlay[]

  /**
   * 当前选中的用于编辑中的 Overlay. 为 `null` 时表示无选中
   */
  selectedOverlay: Overlay | null

  /**
   * 设置当前选中的 Overlay
   * @param overlay 为 Overlay 对象或者其 ID, 或者为 `null` 时表示取消选中
   */
  setSelectedOverlay(overlay: Overlay | number | null): void

  /**
   * 根据 ID 删除指定的 Overlay
   */
  deleteOverlay(id: number): void

  /**
   * 根据 ID 更新指定的 Overlay
   * @param id 要更新的 Overlay 的ID
   * @param updater 当则仅为对象时，覆盖更新对象中存在的字段; 当为函数时则完整覆盖更新
   */
  updateOverlay(
    id: number,
    updater: Partial<SingleOverlayUpdatePayload> | ((overlay: Overlay) => SingleOverlayUpdatePayload)
  ): void

  /**
   * 批量更新多个 overlay
   * @param overlays
   */
  bulkUpdateOverlays(overlays: Array<SingleOverlayUpdatePayload>): void

  resetOverlays(): void
}

function getTracksAfterOverlayUpdated(tracks: Track[], updatedOverlay: SingleOverlayUpdatePayload) {
  if (updatedOverlay.type === OverlayType.STORYBOARD) {
    updatedOverlay.storyboardIndex = undefined
  }

  const { id: targetId } = updatedOverlay

  const targetOverlay = findOverlay(tracks, targetId)
  const sourceTrackIndex = getOverlayTrackIndex(tracks, targetId)

  // 如果找不到 overlay 则跳过
  if (sourceTrackIndex === -1 || !targetOverlay) return tracks

  const cloned = cloneDeep(tracks)
  const originalTrackIndex = getOverlayTrackIndex(tracks, targetId)
  const { targetTrackIndex = originalTrackIndex } = updatedOverlay

  // 如果行号没有改变，就地更新
  if (originalTrackIndex === targetTrackIndex) {
    cloned[sourceTrackIndex] = {
      ...cloned[sourceTrackIndex],
      overlays: cloned[sourceTrackIndex].overlays.map(o =>
        o.id === targetId ? updatedOverlay : o
      )
    }
  } else {
    // 从源 track 中移除
    cloned[sourceTrackIndex] = {
      ...cloned[sourceTrackIndex],
      overlays: tracks[sourceTrackIndex].overlays.filter(o => o.id !== updatedOverlay.id)
    }

    // 添加到目标 track
    cloned[targetTrackIndex] = {
      ...cloned[targetTrackIndex],
      overlays: [...cloned[targetTrackIndex].overlays, updatedOverlay]
    }
  }

  return cloned
}

/**
 * 管理编辑器中 overlay 元素的 Hook
 * Overlay 可以是文本、视频或音频，它们被定位在时间轴上
 * @returns 包含 overlay 状态和管理函数的对象
 */
export function useOverlays(initialTracks: Track[] = []): OverlaysHook {
  const [tracks, setTracks] = useState<Track[]>(ensureEmptyTracksAndSort(initialTracks))

  const updateTracks = useCallback((updater: React.SetStateAction<Track[]>) => {
    setTracks(prevTracks => {
      const newTracks = typeof updater === 'function' ? updater(prevTracks) : updater

      // 确保 tracks 中，对于每种轨道类型(分镜轨道除外，全局与非全局算两种)，都至少包含一条空的轨道(overlays.length===0)
      return ensureEmptyTracksAndSort(newTracks)
    })
  }, [])

  const overlays = useMemo(() => {
    return tracks
      .map(track => track.overlays)
      .flat()
  }, [tracks])

  // 跟踪当前选中用于编辑的 overlay
  const [selectedOverlay, _setSelectedOverlay] = useState<Overlay | null>(null)

  const setSelectedOverlay = useCallback((overlay: Overlay | number | null) => {
    if (typeof overlay === 'number') {
      _setSelectedOverlay(overlays.find(o => o.id === overlay) || null)
    } else {
      _setSelectedOverlay(overlay)
    }
  }, [overlays])

  /**
   * 更新特定 overlay 的属性
   * 支持直接属性更新和函数式更新
   */
  const updateOverlay = useCallback((
    id: number,
    updater: Partial<SingleOverlayUpdatePayload> | ((overlay: Overlay) => SingleOverlayUpdatePayload),
  ) => {
    const updateActiveOverlay = (updatedOverlay: Overlay) => {
      if (selectedOverlay?.id === updatedOverlay.id) {
        setSelectedOverlay(updatedOverlay)
      }
    }

    return updateTracks(prevTracks => {
      const targetOverlay = findOverlay(prevTracks, id)
      if (!targetOverlay) return prevTracks

      // 计算更新后的 overlay
      const updatedOverlay = typeof updater === 'function'
        ? updater(targetOverlay)
        : merge({}, targetOverlay, updater) as SingleOverlayUpdatePayload

      updateActiveOverlay(updatedOverlay)
      return getTracksAfterOverlayUpdated(prevTracks, updatedOverlay)
    })
  }, [selectedOverlay])

  /**
   * 一次性批量更新多个 overlay
   * 在单次状态更新中高效地更新多个 overlay
   * @param overlaysToUpdate 要更新的 overlay 数组
   */
  const bulkUpdateOverlays = useCallback(
    (overlaysToUpdate: Array<SingleOverlayUpdatePayload>) => {
      if (!overlaysToUpdate || overlaysToUpdate.length === 0) return

      return updateTracks(prevTracks => {
        return overlaysToUpdate.reduce(
          (result, update) => getTracksAfterOverlayUpdated(result, update),
          prevTracks
        )
      })
    },
    [selectedOverlay]
  )

  /**
   * 通过 ID 移除 overlay 并清除选择
   */
  const deleteOverlay = useCallback((id: number) => {
    updateTracks(prevTracks => {
      const target = findOverlay(prevTracks, id)
      if (!target) return prevTracks

      const isRemovingStoryboard = target?.type === OverlayType.STORYBOARD

      return prevTracks.map(track => {
        // 当删除分镜时，需要删除该分镜下的所有 Overlay, 并将该分镜后的所有分镜及其下所有 Overlay 都前移以填补空隙
        if (isRemovingStoryboard) {
          // 分镜所在的轨道, 需要前移后方的分镜
          if (track.type === TrackType.STORYBOARD) {
            return {
              ...track,
              overlays: track.overlays.
                filter(o => o.id !== id)
                .map(o => ({
                  ...o,
                  from: o.from > target.from
                    ? o.from - target.durationInFrames
                    : o.from
                }))
            }
          }

          // 非全局轨道, 需要删除分镜下的 Overlay, 并前移后续 Overlay
          if (!track.isGlobalTrack) {
            const removedStoryboardIndex = findStoryboardIndex(prevTracks, target)

            return {
              ...track,
              overlays: track.overlays
                // 删除分镜下的所有 Overlay
                .filter(o => o.storyboardIndex !== removedStoryboardIndex)
                // 前移后续 Overlay
                .map(o => ({
                  ...o,
                  ...(o.storyboardIndex !== undefined && o.storyboardIndex > removedStoryboardIndex && {
                    from: o.from - target?.durationInFrames,
                    storyboardIndex: o.storyboardIndex - 1
                  })
                }))
            }
          }

          // 全局轨道不需要处理
          return track
        }

        // 删除非全局轨道中的 Overlay 时, 需要前移分镜内的后方 Overlay
        if (!track.isGlobalTrack && track.overlays.some(o => o.id === id)) {
          const storyboard = findOverlayStoryboard(prevTracks, target)
          if (!storyboard) return track

          const [, storyboardEnd] = getOverlayTimeRange(storyboard)

          return {
            ...track,
            overlays: track.overlays
              .filter(o => o.id !== id)
              .map(o => ({
                ...o,
                ...(o.from > target.from && o.from < storyboardEnd && {
                  from: o.from - target.durationInFrames
                })
              }))
          }
        }

        return {
          ...track,
          overlays: track.overlays.filter(overlay => overlay.id !== id)
        }
      })
    })
    setSelectedOverlay(null)
  }, [])

  const resetOverlays = useCallback(() => {
    updateTracks(initialTracks)
    setSelectedOverlay(null)
  }, [])

  return {
    tracks,
    setTracksDirectly: setTracks,
    updateTracks,
    overlays,
    selectedOverlay,
    setSelectedOverlay,
    updateOverlay,
    bulkUpdateOverlays,
    deleteOverlay,
    resetOverlays,
  }
}
