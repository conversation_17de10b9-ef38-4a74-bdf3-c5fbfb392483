import React, { createContext, useContext } from 'react'

import { TimelineZoomHook } from './useTimelineZoom'
import { TimelineTrackDnDHook } from './useTimelineTrackDnD'
import { TimelineSnappingHook } from './useTimelineSnapping'
import { TimelineClipboard } from './useTimelineClipboard'
import { TimelineOverlayDnDHook } from './useTimelineOverlayDnD'
import { TimelineOverlayActivation } from './useTimelineOverlayActivation.ts'
import { TimelineTracksLayout } from '@rve/editor/contexts/timeline/useTimelineTracksLayout.ts'

/**
 * Context interface for managing timeline state and interactions.
 * @interface TimelineContextType
 */
interface TimelineContextType extends
  TimelineTrackDnDHook,
  TimelineOverlayDnDHook,
  TimelineZoomHook,
  TimelineOverlayActivation,
  Omit<TimelineSnappingHook, 'snappedLandingPoint'>
{
  layout: TimelineTracksLayout
  clipboard: TimelineClipboard

  isContextMenuOpen: boolean,
  setIsContextMenuOpen(v: boolean): void

  /** Reference to the timeline grid DOM element */
  timelineGridRef: React.RefObject<HTMLDivElement | null>

  /**
   *  Number of currently visible rows in the timeline
   *  @deprecated use `useEditorContext().tracks.length` instead
   */
  visibleRows: number
}

/**
 * Context for sharing timeline state and functionality across components.
 */
export const TimelineContext = createContext<TimelineContextType>({} as any)

export const useTimeline = () => {
  const context = useContext(TimelineContext)
  if (!context) {
    throw new Error('useTimeline must be used within a TimelineProvider')
  }
  return context
}
