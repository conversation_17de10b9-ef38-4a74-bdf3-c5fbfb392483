import React, { useRef, useState } from 'react'
import { useTimelineClipboard } from '@rve/editor/contexts/timeline/useTimelineClipboard.ts'
import { useTimelineZoom } from '@rve/editor/contexts/timeline/useTimelineZoom.tsx'
import { useTimelineTrackDnD } from '@rve/editor/contexts/timeline/useTimelineTrackDnD.ts'
import { useTimelineOverlayActivation } from '@rve/editor/contexts/timeline/useTimelineOverlayActivation.ts'
import { useTimelineTracksLayout } from '@rve/editor/contexts/timeline/useTimelineTracksLayout.ts'
import { useTimelineOverlayDnD } from '@rve/editor/contexts/timeline/useTimelineOverlayDnD.ts'
import { useTimelineSnapping } from '@rve/editor/contexts/timeline/useTimelineSnapping.ts'
import { SNAPPING_CONFIG } from '@rve/editor/constants'
import { TimelineContext, useEditorContext } from '@rve/editor/contexts'

/**
 * Provider component that manages timeline state and makes it available to child components.
 * Combines functionality from multiple hooks to handle timeline interactions.
 *
 * @component
 */
export const TimelineProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  // State for context menu visibility
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false)

  const timelineGridRef = useRef<HTMLDivElement>(null)

  const clipboard = useTimelineClipboard()
  const zoom = useTimelineZoom(timelineGridRef)
  const trackDragAndDrop = useTimelineTrackDnD()
  const overlaySelection = useTimelineOverlayActivation()

  const layout = useTimelineTracksLayout()

  const { landingPoint, ...timelineState } = useTimelineOverlayDnD(
    timelineGridRef,
    zoom.zoomScale,
    isContextMenuOpen
  )

  const { snappedLandingPoint, alignmentLines } = useTimelineSnapping({
    landingPoint,
    isDragging: timelineState.isDragging,
    draggingOverlay: timelineState.draggingOverlay,
    dragInfo: timelineState.dragInfo,
    snapThreshold: SNAPPING_CONFIG.thresholdFrames,
  })

  return (
    <TimelineContext.Provider
      value={{
        ...trackDragAndDrop,
        ...zoom,
        ...timelineState,
        ...overlaySelection,
        clipboard,
        layout,

        isContextMenuOpen,
        setIsContextMenuOpen,

        alignmentLines,
        landingPoint: snappedLandingPoint,
        visibleRows: useEditorContext().tracks.length,
        timelineGridRef,
      }}
    >
      {children}
    </TimelineContext.Provider>
  )
}
