import { useState, useEffect } from 'react'
import { PasterResource } from '@/types/resources.ts'
import { useResource } from './useResource.tsx'

/**
 * Hook for managing sticker loading state
 * Provides a synchronous interface for async sticker loading state
 */
export const useStickerLoadingState = (stickerId: string | number) => {
  const [loadingState, setLoadingState] = useState<PasterResource.StickerLoadingState>({
    coverId: stickerId,
    coverLoaded: false,
    thumbLoaded: false,
    thumbLoading: false,
    fileLoaded: false,
    fileLoading: false,
    currentLayer: PasterResource.LoadingLayer.COVER
  })

  const { getStickerLoadingState } = useResource()

  useEffect(() => {
    let isMounted = true

    const fetchLoadingState = async () => {
      try {
        const state = await getStickerLoadingState(stickerId)
        if (isMounted) {
          setLoadingState(state)
        }
      } catch (error) {
        console.error('获取贴纸加载状态失败:', error)
      }
    }

    fetchLoadingState()

    // 设置定期检查以获取最新状态
    const interval = setInterval(fetchLoadingState, 500)

    return () => {
      isMounted = false
      clearInterval(interval)
    }
  }, [stickerId, getStickerLoadingState])

  return loadingState
}
