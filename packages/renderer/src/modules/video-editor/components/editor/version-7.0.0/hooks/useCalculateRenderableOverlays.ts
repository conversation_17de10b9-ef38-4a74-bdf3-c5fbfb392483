import { useCallback } from 'react'
import { RenderableOverlay, TrackType } from '@app/rve-shared/types'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'
import { calculateRenderableOverlays } from '@rve/editor/utils/overlay-helper.ts'

/**
 * 从原始的轨道中筛选出实际需要渲染的视频
 */
export const useCalculateRenderableOverlays = () => {
  const { tracks } = useEditorContext()
  const { videoActivation, narrationActivation } = useTimeline()

  return useCallback(
    (): RenderableOverlay[] => calculateRenderableOverlays(tracks, {
      [TrackType.VIDEO]: videoActivation,
      [TrackType.NARRATION]: narrationActivation
    }),
    [tracks, videoActivation, narrationActivation]
  )
}
