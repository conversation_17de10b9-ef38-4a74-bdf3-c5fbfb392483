import { useHotkeys } from 'react-hotkeys-hook'
import { ZOOM_CONSTRAINTS } from '../constants'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'

/**
 * A custom hook that sets up keyboard shortcuts for timeline controls
 *
 * Keyboard shortcuts:
 * - Space: Play/Pause
 * - Cmd/Ctrl + Z: Undo
 * - Cmd/Ctrl + Shift + Z or Cmd/Ctrl + Y: Redo
 * - Alt + Plus/=: Zoom in
 * - Alt + Minus/-: Zoom out
 */
export const useTimelineShortcuts = () => {
  const {
    history: { canUndo, canRedo, undo, redo },
    videoPlayer: { togglePlayPause }
  } = useEditorContext()

  const { zoomScale, setZoomScale } = useTimeline()

  useHotkeys(
    'space',
    () => {
      togglePlayPause()
    },
    { preventDefault: true, enableOnFormTags: true },
  )

  useHotkeys(
    'meta+z, ctrl+z',
    () => {
      if (canUndo) undo()
    },
    { preventDefault: true }
  )

  useHotkeys(
    'meta+shift+z, ctrl+shift+z, meta+y, ctrl+y',
    () => {
      if (canRedo) redo()
    },
    { preventDefault: true }
  )

  useHotkeys(
    'alt+=, alt+plus',
    () => {
      setZoomScale(zoomScale + ZOOM_CONSTRAINTS.step)
    },
    { preventDefault: true }
  )

  useHotkeys(
    'alt+-, alt+minus',
    () => {
      setZoomScale(zoomScale - ZOOM_CONSTRAINTS.step)
    },
    {
      keydown: true,
      preventDefault: true,
    },
  )
}
