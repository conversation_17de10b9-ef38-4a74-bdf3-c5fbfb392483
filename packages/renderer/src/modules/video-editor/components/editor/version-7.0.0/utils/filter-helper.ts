export class FilterHelper {

  static extractFilterValue(filter: string, filterName: string, defaultValue: number = 100): number {
    const regex = new RegExp(`${filterName}\\((-?\\d+(?:\\.\\d+)?)(%|deg|px)?\\)`)
    const match = filter.match(regex)
    return match ? parseFloat(match[1]) : defaultValue
  }

  static updateFilter(currentFilter: string, filterName: string, value: number, unit: string = '%'): string {
    const regex = new RegExp(`${filterName}\\(-?\\d+(?:\\.\\d+)?(%|deg|px)?\\)`)
    const newFilterValue = `${filterName}(${value}${unit})`

    if (regex.test(currentFilter)) {
      return currentFilter.replace(regex, newFilterValue)
    } else {
      return `${currentFilter} ${newFilterValue}`.trim()
    }
  }

  static updateMultipleFilters(
    currentFilter: string,
    updates: Array<{ filterName: string; value: number; unit?: string }>
  ): string {
    let result = currentFilter

    for (const update of updates) {
      const { filterName, value, unit = '%' } = update
      result = this.updateFilter(result, filterName, value, unit)
    }

    return result
  }

  static removeFilter(currentFilter: string, filterName: string): string {
    const regex = new RegExp(`\\s*${filterName}\\(-?\\d+(?:\\.\\d+)?(%|deg|px)?\\)`, 'g')
    return currentFilter.replace(regex, '').trim()
  }
}
