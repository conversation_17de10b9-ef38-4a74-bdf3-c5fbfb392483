/**
 * 项目状态存储工具
 *
 * 提供与项目状态缓存交互的函数，用于编辑器状态的自动保存。
 * 现在使用 cache-manager 进行统一的缓存管理。
 */
import { cacheManager } from '@/libs/cache/cache-manager'
import { EditorState, ProjectData } from '@/libs/cache/parts/editor.cache.ts'

// 重新导出类型以保持向后兼容性
export type { EditorState, ProjectData }

/**
 * 保存编辑器状态到指定存储
 * @param projectId 项目ID
 * @param editorState 编辑器状态
 * @param manually 是否手动保存（true: 保存到项目存储，false: 保存到自动保存存储）
 */
export async function saveProjectState(
  projectId: string,
  editorState: EditorState,
  manually = false
): Promise<void> {
  return cacheManager.projectState.saveProjectState(projectId, editorState, manually)
}

/**
 * 从指定存储加载编辑器状态
 * @param projectId 项目ID
 * @param manually 是否从手动保存加载（true: 从项目存储加载，false: 从自动保存存储加载）
 */
export async function loadProjectState(projectId: string, manually = false): Promise<EditorState | null> {
  return cacheManager.projectState.loadProjectState(projectId, manually)
}

/**
 * 清除项目的自动保存数据
 * @param projectId 项目ID
 */
export async function clearAutosave(projectId: string): Promise<void> {
  return cacheManager.projectState.clearAutosave(projectId)
}

/**
 * 检查项目是否有自动保存数据
 * @param projectId 项目ID
 */
export async function hasAutosave(projectId: string): Promise<ProjectData | null> {
  return cacheManager.projectState.hasAutosave(projectId)
}

