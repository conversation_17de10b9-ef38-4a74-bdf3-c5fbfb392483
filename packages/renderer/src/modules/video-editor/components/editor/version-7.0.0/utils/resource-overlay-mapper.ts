import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { Overlay, OverlayType, TrackType } from '@app/rve-shared/types'
import { FPS } from '../constants'

export function mapResourceTypeToOverlayType(resourceType: ResourceType): OverlayType {
  switch (resourceType) {
    case ResourceType.AUDIO:
      return OverlayType.SOUND
    case ResourceType.SOUND:
      return OverlayType.SOUND
    case ResourceType.MUSIC:
      return OverlayType.SOUND
    case ResourceType.STICKER:
      return OverlayType.STICKER
    case ResourceType.FONT:
      return OverlayType.TEXT
    default:
      return OverlayType.STICKER
  }
}

function mapResourceTypeToTrackType(resourceType: ResourceType): TrackType {
  switch (resourceType) {
    case ResourceType.AUDIO:
      return TrackType.SOUND
    case ResourceType.SOUND:
      return TrackType.SOUND
    case ResourceType.MUSIC:
      return TrackType.SOUND
    case ResourceType.STICKER:
      return TrackType.IMAGE
    case ResourceType.FONT:
      return TrackType.TEXT
    default:
      return TrackType.MIXED // 默认为混合轨道
  }
}

/**
 * 检查资源类型是否与轨道类型匹配
 */
export function isResourceAcceptableByTrack(resourceType: ResourceType, trackType: TrackType): boolean {
  const mappedTrackType = mapResourceTypeToTrackType(resourceType)
  return mappedTrackType === trackType || trackType === TrackType.MIXED
}

/**
 * 创建叠加层
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @param position 位置信息
 * @param durationMs 持续时间（毫秒）
 */
export function createOverlayFromResource(
  resourceType: ResourceType, resourceUrl: string, position: {
    from: number;
    row: number
  },
  durationMs: number = 10000,
  title?: string,
): Overlay {
  const overlayType = mapResourceTypeToOverlayType(resourceType)
  const durationInFrames = Math.round(durationMs / 1000 * FPS)

  const baseOverlay = {
    id: Date.now(),
    from: position.from,
    durationInFrames,
    height: 100,
    width: 200,
    left: 0,
    top: 0,
    isDragging: false,
    rotation: 0,
  }

  switch (overlayType) {
    case OverlayType.SOUND:
      return {
        ...baseOverlay,
        type: OverlayType.SOUND,
        content: resourceUrl,
        src: resourceUrl,
        styles: {
          volume: 1,
        },
      }
    case OverlayType.STICKER:
      return {
        ...baseOverlay,
        type: OverlayType.STICKER,
        src: resourceUrl,
        content: resourceUrl,
        styles: {
          zIndex: 10,
        },
      }
    case OverlayType.TEXT:
      return {
        ...baseOverlay,
        type: OverlayType.TEXT,
        src: '',
        content: title || '文本',
        styles: {
          fontSize: 24,
          fontWeight: 'normal',
          fontFamily: 'Arial',
          fontStyle: 'normal',
          underlineEnabled: false,
          color: '#ffffff',
          backgroundColor: 'transparent',
          textAlign: 'center',
        },
      }
    default:
      throw new Error(`不支持的叠加层类型: ${overlayType}`)
  }
}
