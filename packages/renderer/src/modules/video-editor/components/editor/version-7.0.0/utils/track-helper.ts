import { IndexableOverlay, Overlay, OverlayType, Track, TrackType } from '@app/rve-shared/types'
import { byStartFrame } from '@rve/editor/utils/overlay-helper.ts'

export const MAP_OVERLAY_TO_TRACK: Record<OverlayType, TrackType> = {
  [OverlayType.STORYBOARD]: TrackType.STORYBOARD,
  [OverlayType.TEXT]: TrackType.TEXT,
  [OverlayType.VIDEO]: TrackType.VIDEO,
  [OverlayType.SOUND]: TrackType.SOUND,
  [OverlayType.CAPTION]: TrackType.TEXT,
  [OverlayType.STICKER]: TrackType.IMAGE,
  [OverlayType.NARRATION]: TrackType.NARRATION,
  [OverlayType.material]: TrackType.MIXED,
}

/**
 * 检查 Overlay 是否可以添加到指定的 Track
 */
export function isOverlayAcceptableByTrack(overlay: Pick<Overlay, 'type'>, track: Pick<Track, 'type'>): boolean {
  if (overlay.type === OverlayType.STORYBOARD) {
    return track.type === TrackType.STORYBOARD
  }

  if (track.type === TrackType.MIXED) return true

  if (track.type === TrackType.NARRATION) {
    return overlay.type === OverlayType.SOUND || overlay.type === OverlayType.TEXT
  }

  return MAP_OVERLAY_TO_TRACK[overlay.type] === track.type
}

/**
 * 根据起始帧查找所在分镜
 */
export function findStoryboardByFromFrame(
  tracks: Track[],
  fromFrame: number
): IndexableOverlay | null {
  const storyboards = tracks
    .find(o => o.type === TrackType.STORYBOARD)!
    .overlays
    .filter(o => o.type === OverlayType.STORYBOARD)
    .sort(byStartFrame())

  if (!storyboards) {
    return null
  }

  let endFrame = 0
  for (let index = 0; index < storyboards.length; index++) {
    const storyboard = storyboards[index]
    endFrame += storyboard.durationInFrames

    if (endFrame > fromFrame) {
      return {
        ...storyboard,
        index,
      }
    }
  }

  // 找不到时, 说明目标帧超出最后一个分镜的终止帧, 则默认返回最后一个分镜
  return {
    ...storyboards.at(-1)!,
    index: storyboards.length - 1,
  }
}

/**
 * 生成新的 Overlay ID
 */
export function generateNewOverlayId(tracks: Track[]) {
  const overlays = tracks.map(t => t.overlays).flat()
  if (!overlays.length) return 1

  return Math.max(...overlays.map(o => o.id)) + 1
}

/**
 * 获取轨道类型对应的中文标签
 * @param trackType 轨道类型
 * @returns 中文标签
 */
export function getTrackTypeLabel(trackType: TrackType): string {
  switch (trackType) {
    case TrackType.VIDEO:
      return '视频'
    case TrackType.SOUND:
      return '音频'
    case TrackType.TEXT:
      return '文本'
    case TrackType.IMAGE:
      return '图片'
    case TrackType.MIXED:
      return '混合'
    case TrackType.NARRATION:
      return '口播'
    case TrackType.STORYBOARD:
      return '分镜'
    default:
      return '内容'
  }
}

/**
 * 确保每种轨道类型的末尾包含且仅包含一条空轨道，并按规则排序
 */
export function ensureEmptyTracksAndSort(tracks: Track[]): Track[] {
  /**
   * 轨道类型的唯一标识符，用于区分不同的轨道类型组合
   */
  type TrackTypeKey = string

  /**
   * 获取轨道类型的排序优先级
   */
  const _getTrackTypePriority = (type: TrackType): number => {
    switch (type) {
      case TrackType.STORYBOARD:
        return 0 // 分镜轨道最高优先级
      case TrackType.VIDEO:
        return 1
      case TrackType.NARRATION:
        return 2
      case TrackType.TEXT:
        return 3
      case TrackType.IMAGE:
        return 4
      case TrackType.SOUND:
        return 5
      case TrackType.MIXED:
        return 6
      default:
        return 7
    }
  }

  // 初始化统计信息
  const _initializeTrackTypeStats = () => {
    [
      TrackType.VIDEO,
      TrackType.NARRATION
    ].forEach(type => {
      trackTypeStats.set(_getTrackTypeKey({ type, isGlobalTrack: false }), { lastTrackEmpty: false, lastIndex: -1 })
    });

    [
      TrackType.TEXT,
      TrackType.SOUND,
      TrackType.IMAGE,
      TrackType.MIXED
    ].forEach(type => {
      trackTypeStats.set(_getTrackTypeKey({ type, isGlobalTrack: true }), { lastTrackEmpty: false, lastIndex: -1 })
    })
  }

  /**
   * 生成轨道类型的唯一标识符
   */
  const _getTrackTypeKey = (track: Pick<Track, 'type' | 'isGlobalTrack'>): TrackTypeKey => `${track.type}-${track.isGlobalTrack ? 'global' : 'local'}`

  const result: Track[] = [...tracks]

  // 统计每种轨道类型的情况
  const trackTypeStats = new Map<TrackTypeKey, { lastTrackEmpty: boolean; lastIndex: number }>()
  _initializeTrackTypeStats()

  // 遍历轨道，更新统计信息
  result.forEach((track, index) => {
    if (track.type === TrackType.STORYBOARD) return

    const key = _getTrackTypeKey(track)
    const stats = trackTypeStats.get(key)
    if (stats) {
      stats.lastIndex = index
      stats.lastTrackEmpty = track.overlays.length === 0
    }
  })

  // 为需要的轨道类型添加空轨道
  const tracksToAdd: { track: Track; insertAfterIndex: number }[] = []

  trackTypeStats.forEach((stats, typeKey) => {
    // 如果该类型没有空轨道，需要添加一条
    if (!stats.lastTrackEmpty) {
      const [type, isGlobalStr] = typeKey.split('-')
      const isGlobalTrack = isGlobalStr === 'global'

      const emptyTrack: Track = {
        type: type as TrackType,
        isGlobalTrack,
        overlays: []
      }

      // 将空轨道添加到该类型的最后一个轨道之后
      tracksToAdd.push({
        track: emptyTrack,
        insertAfterIndex: stats.lastIndex
      })
    }
  })

  // 按插入位置从后往前排序，避免插入时索引偏移
  tracksToAdd.sort((a, b) => b.insertAfterIndex - a.insertAfterIndex)

  // 插入空轨道
  tracksToAdd.forEach(({ track, insertAfterIndex }) => {
    result.splice(insertAfterIndex + 1, 0, track)
  })

  // 5. 删除每种轨道类型组末尾多余的连续空轨道，只保留最后一条
  const tracksToRemove: number[] = []

  // 重新统计轨道类型信息（因为可能添加了新轨道）
  const updatedTrackTypeStats = new Map<TrackTypeKey, { indices: number[] }>()
  _initializeTrackTypeStats()

  // 收集每种类型的轨道索引
  result.forEach((track, index) => {
    if (track.type === TrackType.STORYBOARD) return

    const key = _getTrackTypeKey(track)
    const stats = updatedTrackTypeStats.get(key)
    if (stats) {
      stats.indices.push(index)
    }
  })

  // 对每种轨道类型，检查末尾的连续空轨道
  updatedTrackTypeStats.forEach(stats => {
    if (stats.indices.length <= 1) return // 只有一条或没有轨道，无需处理

    const indices = stats.indices
    let consecutiveEmptyCount = 0

    // 从该类型的最后一个轨道开始向前遍历，统计末尾连续空轨道数量
    for (let i = indices.length - 1; i >= 0; i--) {
      const trackIndex = indices[i]
      const track = result[trackIndex]

      if (track.overlays.length === 0) {
        consecutiveEmptyCount++
      } else {
        break // 遇到非空轨道，停止统计
      }
    }

    // 如果末尾有多条连续空轨道，标记除最后一条外的所有空轨道为待删除
    if (consecutiveEmptyCount > 1) {
      for (let i = 0; i < consecutiveEmptyCount - 1; i++) {
        const trackIndexToRemove = indices[indices.length - 1 - consecutiveEmptyCount + 1 + i]
        tracksToRemove.push(trackIndexToRemove)
      }
    }
  })

  // 按索引从大到小排序，避免删除时索引偏移
  tracksToRemove.sort((a, b) => b - a)

  // 删除多余的空轨道
  tracksToRemove.forEach(index => {
    result.splice(index, 1)
  })

  if (!result.some(o => o.type === TrackType.STORYBOARD)) {
    result.push({
      type: TrackType.STORYBOARD,
      overlays: [],
    })
  }

  // 6. 最终排序（只按基本规则排序，不调整空轨道位置）
  return result.sort((a, b) => {
    // 最高优先级：分镜轨道永远在最前面
    if (a.type === TrackType.STORYBOARD && b.type !== TrackType.STORYBOARD) return -1
    if (a.type !== TrackType.STORYBOARD && b.type === TrackType.STORYBOARD) return 1

    // 第一优先级：isGlobalTrack（非全局在前，全局在后）
    const aIsGlobal = a.isGlobalTrack ?? false
    const bIsGlobal = b.isGlobalTrack ?? false
    if (aIsGlobal !== bIsGlobal) {
      return aIsGlobal ? 1 : -1
    }

    // 第二优先级：type（按优先级排序）
    const aPriority = _getTrackTypePriority(a.type)
    const bPriority = _getTrackTypePriority(b.type)
    if (aPriority !== bPriority) {
      return aPriority - bPriority
    }

    return 0
  })
}
