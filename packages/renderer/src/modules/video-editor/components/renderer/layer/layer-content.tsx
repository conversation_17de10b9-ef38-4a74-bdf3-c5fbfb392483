import React from 'react'

import {
  <PERSON><PERSON><PERSON>er<PERSON>ontent,
  SoundLayer<PERSON>ontent,
  CaptionLayerContent,
  StickerLayerContent,
  TextLayerContent
} from './layers'

import { Overlay, OverlayType } from '@app/rve-shared/types'

interface LayerContentProps {
  overlay: Overlay
  baseUrl?: string
}

const COMMON_STYLE: React.CSSProperties = {
  width: '100%',
  height: '100%',
}

export const LayerContent: React.FC<LayerContentProps> = ({
  overlay, baseUrl,
}) => {
  switch (overlay.type) {
    case OverlayType.VIDEO:
      return (
        <div style={{ ...COMMON_STYLE }}>
          <VideoLayerContent overlay={overlay} baseUrl={baseUrl} />
        </div>
      )

    case OverlayType.TEXT:
      return (
        <div style={{ ...COMMON_STYLE }}>
          <TextLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.CAPTION:
      return (
        <div
          style={{
            ...COMMON_STYLE,
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
          }}
        >
          <CaptionLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.STICKER:
      return (
        <div style={{ ...COMMON_STYLE }} className="layer-content-wrapper">
          <StickerLayerContent overlay={overlay} />
        </div>
      )

    case OverlayType.SOUND:
      return <SoundLayerContent overlay={overlay} baseUrl={baseUrl} />

    default:
      return null
  }
}
