import React from 'react'
import { Audio, useCurrentFrame, interpolate, Easing } from 'remotion'
import { SoundOverlay } from '@app/rve-shared/types'
import { toAbsoluteUrl } from '@app/rve-shared/utils'
import { useRenderContext } from '@rve/renderer'

interface SoundLayerContentProps {
  overlay: SoundOverlay
  baseUrl?: string
}

export const SoundLayerContent: React.FC<SoundLayerContentProps> = ({
  overlay,
  baseUrl,
}) => {
  const currentFrame = useCurrentFrame()
  const { playerMetadata: { fps } } = useRenderContext()

  // Determine the audio source URL
  let audioSrc = overlay.src

  // If it's a relative URL and baseUrl is provided, use baseUrl
  if (overlay.src.startsWith('/') && baseUrl) {
    audioSrc = `${baseUrl}${overlay.src}`
  }
  // Otherwise use the toAbsoluteUrl helper for relative URLs
  else if (overlay.src.startsWith('/')) {
    audioSrc = toAbsoluteUrl(overlay.src)
  }

  // 计算淡入淡出效果的音量
  let finalVolume = overlay.styles?.volume ?? 1

  // 获取淡入淡出时长（秒转换为帧数）
  const fadeInDuration = overlay.fadeInDuration ?? 0
  const fadeOutDuration = overlay.fadeOutDuration ?? 0
  const fadeInFrames = fadeInDuration * fps
  const fadeOutFrames = fadeOutDuration * fps
  const totalFrames = overlay.durationInFrames

  // 计算淡入效果
  if (fadeInFrames > 0 && currentFrame < fadeInFrames) {
    const fadeInProgress = interpolate(
      currentFrame,
      [0, fadeInFrames],
      [0, 1],
      {
        extrapolateLeft: 'clamp',
        extrapolateRight: 'clamp',
        easing: Easing.out(Easing.cubic)
      }
    )
    finalVolume *= fadeInProgress
  }

  // 计算淡出效果
  if (fadeOutFrames > 0 && currentFrame > totalFrames - fadeOutFrames) {
    const fadeOutProgress = interpolate(
      currentFrame,
      [totalFrames - fadeOutFrames, totalFrames],
      [1, 0],
      {
        extrapolateLeft: 'clamp',
        extrapolateRight: 'clamp',
        easing: Easing.out(Easing.cubic)
      }
    )
    finalVolume *= fadeOutProgress
  }

  return (
    <Audio
      src={audioSrc}
      startFrom={overlay.startFromSound || 0}
      volume={finalVolume}
      playbackRate={overlay.speed ?? 1}
    />
  )
}
