import React from 'react'
import { useCurrentFrame } from 'remotion'
import { TextOverlay } from '@app/rve-shared/types'
import { useRenderContext } from '@rve/renderer'
import { animationTemplates } from '@app/rve-shared/constants'
import { CloudTextRenderer } from './cloud-layer-text-renderer.tsx'

interface TextLayerContentProps {
  overlay: TextOverlay
}

export const TextLayerContent: React.FC<TextLayerContentProps> = ({ overlay }) => {
  const frame = useCurrentFrame()
  const { playerMetadata: { fps } } = useRenderContext()

  const isExitPhase = frame >= overlay.durationInFrames - fps

  const enterAnimation = !isExitPhase && overlay.styles.animation?.enter
    ? animationTemplates[overlay.styles.animation.enter]?.enter(
      frame,
      overlay.durationInFrames,
    )
    : {}

  const exitAnimation = isExitPhase && overlay.styles.animation?.exit
    ? animationTemplates[overlay.styles.animation.exit]?.exit(
      frame,
      overlay.durationInFrames,
    )
    : {}

  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center', // Center vertically
    textAlign: overlay.styles.textAlign,
    justifyContent:
      overlay.styles.textAlign === 'center'
        ? 'center'
        : overlay.styles.textAlign === 'right'
          ? 'flex-end'
          : 'flex-start',
    overflow: 'hidden',
    // 确保容器能够容纳换行文本
    boxSizing: 'border-box',
    ...(isExitPhase ? exitAnimation : enterAnimation),
  }

  // // 生成完整的文本样式，包含描边和阴影效果
  // const baseTextStyles = generateTextCSS({
  //   ...overlay.styles,
  //   fontSize: finalFontSize,
  //   fontFamily: getFontFamily(overlay.styles.fontFamily),
  // })

  return (
    <CloudTextRenderer
      overlay={overlay}
      containerStyle={containerStyle}
    />
  )
}
