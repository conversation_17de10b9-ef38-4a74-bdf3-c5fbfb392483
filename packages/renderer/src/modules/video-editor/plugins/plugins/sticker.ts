import React from 'react'
import { Sticker } from 'lucide-react'
import { OverlayType } from '@app/rve-shared/types'
import { registerMaterialPlugin } from '../registry'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

/**
 * 贴纸素材插件
 * 将现有的贴纸面板功能封装为插件
 */

const Panel = React.lazy(() =>
  import('@rve/editor/components/plugin-panels/stickers.tsx')
)

export default registerMaterialPlugin({
  id: ResourceType.STICKER,
  title: '贴纸',
  icon: Sticker,
  component: Panel,
  overlayType: OverlayType.STICKER,
  order: 10,
})
