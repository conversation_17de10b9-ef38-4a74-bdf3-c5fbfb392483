import { OverlayType } from '@app/rve-shared/types'
import { LucideIcon } from 'lucide-react'
import React from 'react'

/**
 * 素材插件的核心接口定义
 * 每个素材插件必须实现这个接口
 */
export interface MaterialPlugin {
  /**
   * 插件的唯一标识符，推荐使用kebab-case格式
   * 例如: 'video', 'text', 'audio', 'caption'
   */
  id: string;

  /**
   * 插件的显示名称，将显示在侧边栏按钮上
   */
  title: string;

  /**
   * 插件的图标组件，使用lucide-react图标
   */
  icon: LucideIcon;

  /**
   * 插件的面板组件，使用React.lazy进行懒加载
   * 这个组件将在用户点击对应的侧边栏按钮时加载和渲染
   */
  component: React.LazyExoticComponent<React.ComponentType<any>>;

  /**
   * 对应的OverlayType枚举值，用于兼容现有代码
   * 这个字段允许插件系统与现有的OverlayType系统无缝集成
   */
  overlayType: OverlayType;

  /**
   * 插件的排序权重，数字越小排序越靠前
   * 默认为100
   */
  order?: number;

  /**
   * 可选的初始化函数，在插件首次注册时调用
   * 可以用于设置事件监听器、初始化资源等
   */
  initialize?: () => void | Promise<void>;

  /**
   * 可选的清理函数，在插件被卸载时调用
   * 可以用于清理事件监听器、释放资源等
   */
  cleanup?: () => void | Promise<void>;
}

/**
 * 插件注册选项，允许在注册时提供额外配置
 */
export interface PluginRegistrationOptions {
  /**
   * 是否覆盖已存在的同ID插件
   * 默认为false，即不允许覆盖
   */
  override?: boolean;
}
