import React, { useState, useMemo, forwardRef, useImperativeHandle } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Checkbox } from '@/components/ui/checkbox'
import { SearchIcon, X, ChevronDown } from 'lucide-react'
import { MatrixModule } from '@/libs/request/api/matrix'
import { useQuery } from '@tanstack/react-query'
import { formatTimestamp } from '@/components/lib/utils'
import { DateRangePicker } from '@/components/date-range-picker'
import { type DateRange } from 'react-day-picker'
import { usePaginationPlanDetailList } from '@/hooks/queries/useQueryMatrix'
import { Account, AccountPushDetail } from '@/types/matrix/douyin.ts'

export interface AccountDetailDrawerRef {
  open: (account: Account) => void
  close: () => void
}

const publishStatusMap = {
  0: { label: '待发布', variant: 'secondary' as const },
  1: { label: '发布成功', variant: 'default' as const },
  2: { label: '发布失败', variant: 'destructive' as const },
  3: { label: '发布中', variant: 'secondary' as const },
}

export const AccountDetailDrawer = forwardRef<AccountDetailDrawerRef, any>(
  (_props, ref) => {
    const [open, setOpen] = useState(false)
    const [currentAccount, setCurrentAccount] = useState<Account | null>(null)
    const [searchTerm, setSearchTerm] = useState('')
    const [statusFilter, setStatusFilter] = useState<string>('all')
    const [dateRange, setDateRange] = useState<DateRange | undefined>()
    const [selectedPlanIds, setSelectedPlanIds] = useState<number[]>([])
    const [planPopoverOpen, setPlanPopoverOpen] = useState(false)

    // 获取发布计划列表
    const { data: planListResponse } = useQuery({
      queryKey: ['accountPlanList', currentAccount?.id],
      queryFn: () => MatrixModule.dyAccount.accountPlanList({
        accountIds: currentAccount?.id ? [currentAccount.id] : [],
        limit: 100
      }),
      enabled: open && !!currentAccount?.id,
    })

    // 处理计划列表数据
    const planList = useMemo(() => {
      if (!planListResponse) return []
      // 如果返回的是数组，直接使用；如果是单个对象，包装成数组
      return Array.isArray(planListResponse) ? planListResponse : [planListResponse]
    }, [planListResponse])

    // 构建搜索参数
    const searchParams = useMemo(() => {
      const createTime = dateRange?.from && dateRange?.to
        ? [dateRange.from.getTime(), dateRange.to.getTime()] as [number, number]
        : undefined

      return {
        accountId: currentAccount?.id || 0,
        createTime,
        status: statusFilter !== 'all' ? Number(statusFilter) : undefined,
        planIds: selectedPlanIds.length > 0 ? selectedPlanIds : undefined,
      }
    }, [currentAccount?.id, dateRange, statusFilter, selectedPlanIds])

    const {
      data: pushDetailList,
      pagination,
      isLoading,
      isError,
      setPagination
    } = usePaginationPlanDetailList(
      searchParams,
      20,
      open && !!currentAccount?.id,
    )

    useImperativeHandle(ref, () => ({
      open: (account: Account) => {
        setCurrentAccount(account)
        setSearchTerm('')
        setStatusFilter('all')
        setDateRange(undefined)
        setSelectedPlanIds([])
        setPlanPopoverOpen(false)
        setPagination({ pageIndex: 0, pageSize: 20 })
        setOpen(true)
      },
      close: () => {
        setOpen(false)
        setCurrentAccount(null)
        setSearchTerm('')
        setStatusFilter('all')
        setDateRange(undefined)
        setSelectedPlanIds([])
        setPlanPopoverOpen(false)
        setPagination({ pageIndex: 0, pageSize: 20 })
      }
    }), [setPagination])

    // 表格列定义
    const columns: ColumnDef<AccountPushDetail>[] = [
      {
        id: 'video',
        header: '视频',
        cell: ({ row }) => {
          const item = row.original
          return (
            <div className="flex items-center space-x-3">
              <div className="w-16 h-12 bg-gray-100 rounded overflow-hidden">
                {item.videoCover ? (
                  <img
                    src={item.videoCover}
                    alt="视频封面"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-xs text-gray-400">
                    无封面
                  </div>
                )}
              </div>
            </div>
          )
        },
        size: 80,
      },
      {
        accessorKey: 'title',
        header: '标题',
        cell: ({ row }) => {
          const title = row.getValue('title') as string
          return (
            <div className="max-w-[200px]">
              <div className="font-medium truncate">{title || '--'}</div>
            </div>
          )
        },
      },
      {
        accessorKey: 'status',
        header: '发布状态',
        cell: ({ row }) => {
          const status = row.getValue('status') as number
          const statusInfo = publishStatusMap[status as keyof typeof publishStatusMap]
          return (
            <Badge variant={statusInfo?.variant || 'secondary'}>
              {statusInfo?.label || '未知'}
            </Badge>
          )
        },
        size: 100,
      },
      {
        accessorKey: 'playNum',
        header: '播放数',
        cell: ({ row }) => {
          const playNum = row.getValue('playNum') as number
          return playNum || 0
        },
        size: 100,
      },
      {
        accessorKey: 'diggNum',
        header: '点赞数',
        cell: ({ row }) => {
          const diggNum = row.getValue('diggNum') as number
          return diggNum || 0
        },
        size: 100,
      },
      {
        accessorKey: 'shareNum',
        header: '分享数',
        cell: ({ row }) => {
          const shareNum = row.getValue('shareNum') as number
          return shareNum || 0
        },
        size: 100,
      },
      {
        accessorKey: 'createTime',
        header: '发布时间',
        cell: ({ row }) => {
          const createTime = row.getValue('createTime') as number
          return formatTimestamp(createTime)
        },
        size: 150,
      },
      {
        id: 'actions',
        header: () => <div className="text-right">操作</div>,
        cell: () => {
          return (
            <div className="flex gap-2 justify-end">
              <Button variant="link" size="sm">删除</Button>
            </div>
          )
        },
        size: 80,
      },
    ]

    return (
      <Drawer
        direction="right"
        open={open}
        onOpenChange={setOpen}
      >
        <DrawerContent className="max-h-screen data-[vaul-drawer-direction=right]:w-[800px] ">
          <DrawerHeader className="border-b">
            <div className="flex items-center justify-between">
              <div>
                <DrawerTitle>查看详情</DrawerTitle>
                <DrawerDescription>
                  当前账号:
                  <span className="text-red-500 font-medium">
                    {currentAccount?.nickname || currentAccount?.orgNickname || '未知账户'}
                  </span>
                </DrawerDescription>
              </div>
              <DrawerClose asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <X className="h-4 w-4" />
                </Button>
              </DrawerClose>
            </div>
          </DrawerHeader>

          {/* 账户统计信息 */}
          <div className="p-6 border-b bg-neutral-800">
            <div className="flex items-center space-x-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={currentAccount?.avatar} />
                <AvatarFallback>
                  {currentAccount?.nickname?.charAt(0).toUpperCase() ||
                   currentAccount?.orgNickname?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="text-sm text-gray-600">
                  ID: {currentAccount?.id} | 地址: {currentAccount?.province || '--'}
                </div>
              </div>
              <div className="grid grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold">{currentAccount?.fanNum || 0}</div>
                  <div className="text-xs text-gray-500">粉丝数</div>
                </div>
                <div>
                  <div className="text-lg font-bold">{currentAccount?.likeNum || 0}</div>
                  <div className="text-xs text-gray-500">点赞数</div>
                </div>
                <div>
                  <div className="text-lg font-bold">{currentAccount?.commentNum || 0}</div>
                  <div className="text-xs text-gray-500">评论数</div>
                </div>
                <div>
                  <div className="text-lg font-bold">{currentAccount?.publishNum || 0}</div>
                  <div className="text-xs text-gray-500">分享数</div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex-1 flex flex-col overflow-hidden px-6 mt-3">
            {/* 筛选区域 */}
            <div className="p-4 border-b space-y-3">
              <div className="text-sm font-medium">视频动态</div>
              <div className="flex items-center space-x-3">
                {/* 搜索框 */}
                <div className="relative flex-1">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="搜索标题或内容"
                    value={searchTerm}
                    onChange={e => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* 状态筛选 */}
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="发布状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="0">待发布</SelectItem>
                    <SelectItem value="1">发布成功</SelectItem>
                    <SelectItem value="2">发布失败</SelectItem>
                    <SelectItem value="3">发布中</SelectItem>
                  </SelectContent>
                </Select>

                {/* 发布计划筛选 */}
                <Popover open={planPopoverOpen} onOpenChange={setPlanPopoverOpen}>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-40 justify-between">
                      {selectedPlanIds.length > 0
                        ? `已选 ${selectedPlanIds.length} 个计划`
                        : '发布计划'}
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60 p-0" align="start">
                    <div className="p-3">
                      <div className="text-sm font-medium mb-2">选择发布计划</div>
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {planList.length === 0 ? (
                          <div className="text-sm text-gray-500 py-2">暂无发布计划</div>
                        ) : (
                          planList.map((plan: { id: number, name: string }) => (
                            <div key={plan.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={`plan-${plan.id}`}
                                checked={selectedPlanIds.includes(plan.id)}
                                onCheckedChange={checked => {
                                  if (checked) {
                                    setSelectedPlanIds(prev => [...prev, plan.id])
                                  } else {
                                    setSelectedPlanIds(prev => prev.filter(id => id !== plan.id))
                                  }
                                }}
                              />
                              <label
                                htmlFor={`plan-${plan.id}`}
                                className="text-sm cursor-pointer flex-1"
                              >
                                {plan.name}
                              </label>
                            </div>
                          ))
                        )}
                      </div>
                      {selectedPlanIds.length > 0 && (
                        <div className="mt-3 pt-2 border-t">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedPlanIds([])}
                            className="w-full"
                          >
                            清空选择
                          </Button>
                        </div>
                      )}
                    </div>
                  </PopoverContent>
                </Popover>

                {/* 时间筛选 */}
                <DateRangePicker
                  value={dateRange}
                  onChange={setDateRange}
                />

                {/* 导出按钮 */}
                <Button variant="outline" size="sm">
                  导出数据
                </Button>
              </div>
            </div>

            {/* 数据表格 */}
            <div className="flex-1 overflow-hidden">
              <DataTable
                columns={columns}
                data={pushDetailList || []}
                pagination={pagination}
                onPaginationChange={setPagination}
                loading={isLoading}
                emptyMessage={isError ? '加载数据失败，请重试' : '暂无视频动态数据'}
              />
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    )
  }
)

AccountDetailDrawer.displayName = 'AccountDetailDrawer'
