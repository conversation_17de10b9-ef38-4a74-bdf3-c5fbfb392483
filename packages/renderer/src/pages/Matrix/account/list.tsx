import React, { useMemo, useState, useRef } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { GroupSidebar } from '@/components/ui/group-sidebar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { SearchIcon, PlusIcon } from 'lucide-react'

import { useQueryAccountGroup } from '@/hooks/queries/useQueryAccountGroup'
import { usePagination } from '@/hooks/usePagination'
import { MatrixModule } from '@/libs/request/api/matrix'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useDebounceValue } from '@/hooks/useDebounceValue'
import { SelectAccountDrawer, SelectAccountDrawerRef } from '@/pages/Matrix/account/components/SelectAccountDrawer'
import useRequest from '@/hooks/useRequest'
import { queryClient } from '@/main'
import { isEmpty } from 'lodash'
import { formatTimestamp } from '@/components/lib/utils'
import { Account, GroupAccount, GroupAccountSearchParams } from '@/types/matrix/douyin.ts'

export const AccountList = () => {
  const [selectedGroupId, setSelectedGroupId] = useState<number>(0)
  const [searchTerm, setSearchTerm] = useState('')
  const selectAccountDrawerRef = useRef<SelectAccountDrawerRef>(null)

  const { data: groups } = useQueryAccountGroup()

  const debouncedSearchTerm = useDebounceValue(searchTerm, 500)

  const { mutate: handleAddAccounts } = useRequest(
    ( accounts: Account[] ) => {
      if (!accounts || isEmpty(accounts)) return Promise.reject('请选择账户')
      return MatrixModule.addAccountToGroup({
        groupId: selectedGroupId,
        accountIds: accounts.map(o => o.id)
      })
    },
    {
      actionName: '添加账户到分组',
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.GROUP_ACCOUNT_LIST] })
      }
    }
  )

  const { mutate: handleDeleteAccounts, isPending: deleting } = useRequest(
    ( accountId: number ) => {
      if (!accountId) return Promise.reject('请选择账户')
      return MatrixModule.deleteAccountFromGroup({
        groupId: selectedGroupId,
        accountIds: [accountId],
      })
    },
    {
      actionName: '从分组中删除账户',
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.GROUP_ACCOUNT_LIST] })
      }
    }
  )

  const searchParams = useMemo(() => ({
    groupId: selectedGroupId,
    search: debouncedSearchTerm || undefined,
  }), [selectedGroupId, debouncedSearchTerm])

  const {
    data: accounts,
    pagination,
    isLoading,
    isError,
    setPagination,
  } = usePagination<GroupAccount, GroupAccountSearchParams>({
    queryKey: [QUERY_KEYS.GROUP_ACCOUNT_LIST],
    queryFn: MatrixModule.groupAccount,
    searchParams,
    initialPageSize: 10,
    enabled: selectedGroupId > 0,
  })

  const columns: ColumnDef<GroupAccount>[] = [
    {
      accessorKey: 'nickname',
      header: '账号',
      cell: ({ row }) => {
        const account = row.original
        return (
          <div className="flex items-center space-x-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src={account.avatar} />
              <AvatarFallback>
                {account.nickname?.charAt(0).toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            <span className="font-medium">{account.nickname || '未知账户'}</span>
          </div>
        )
      },
    },
    {
      accessorKey: 'createTime',
      header: '创建时间',
      cell: ({ row }) => {
        const createTime = row.getValue('createTime') as number
        return createTime ? formatTimestamp(createTime) : '-'
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const account = row.original
        return (
          <Button
            disabled={deleting}
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteAccounts(account.id)}
          >
            删除
          </Button>
        )
      },
    },
  ]

  return (
    <div className="flex h-full bg-background/50 rounded-lg overflow-auto">
      {/* 左侧分组栏 */}
      <div className="w-64 border-r ">
        <GroupSidebar
          groups={groups || []}
          selectedGroupId={selectedGroupId}
          onGroupSelect={setSelectedGroupId}

        />
      </div>

      <div className="flex-1 flex flex-col">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">

            <h2 className="text-lg font-semibold">{groups?.find(group => group.id === selectedGroupId)?.name || '未分组'}</h2>
            <div className="flex items-center space-x-2">

              <Button
                size="sm"
                onClick={() => selectAccountDrawerRef.current?.open({
                  groupId: selectedGroupId,
                  title: '添加账户到分组',
                  description: `选择要添加到 "${groups?.find(group => group.id === selectedGroupId)?.name || '未分组'}" 的账户`,
                })}
                disabled={selectedGroupId === 0}
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                新增账户
              </Button>
            </div>
          </div>

          {/* 搜索栏 */}
          <div className="mt-4 flex items-center space-x-2">
            <div className="relative flex-1 max-w-sm">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索账户"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
        </div>

        <div className="flex-1 p-4">
          {selectedGroupId === 0 ? (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              请选择一个分组查看账户列表
            </div>
          ) : (
            <DataTable
              columns={columns}
              data={accounts}
              pagination={pagination}
              onPaginationChange={setPagination}
              loading={isLoading}
              emptyMessage={isError ? '加载失败，请重试' : '暂无账户数据'}
            />
          )}
        </div>
      </div>

      {/* 选择账户抽屉 */}
      <SelectAccountDrawer
        ref={selectAccountDrawerRef}
        onConfirm={handleAddAccounts}
      />
    </div>
  )
}
