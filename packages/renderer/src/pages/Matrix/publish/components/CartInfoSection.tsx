import React, { useState } from 'react'
import { Control, FieldErrors, useFieldArray, useWatch } from 'react-hook-form'
import { Users, Plus, Trash2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Account, AccountProduct, AccountProductProduct } from '@/types/matrix/douyin.ts'

interface CartInfoSectionProps {
  control: Control<any>
  errors?: FieldErrors
  selectedAccounts: Account[]
}

export const CartInfoSection: React.FC<CartInfoSectionProps> = ({
  control,
  errors,
  selectedAccounts,
}) => {
  const [expandedAccounts, setExpandedAccounts] = useState<Set<string>>(new Set())

  // 使用 useFieldArray 管理 accountProducts
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: 'accountProducts',
  })

  // 监听当前的 accountProducts 值
  const accountProducts = useWatch({
    control,
    name: 'accountProducts',
    defaultValue: [],
  }) as AccountProduct[]

  // 获取指定账号的商品配置和索引
  const getAccountProductsInfo = (accountId: string) => {
    const accountIndex = fields.findIndex((field: any) => field.accountId === accountId)
    const accountProduct = accountProducts[accountIndex]
    return {
      accountIndex,
      products: accountProduct?.products || [],
    }
  }

  // 添加商品到指定账号
  const addProductToAccount = (accountId: string) => {
    const { accountIndex, products } = getAccountProductsInfo(accountId)
    const newProduct = { title: '', url: '' }

    if (accountIndex >= 0) {
      // 更新现有账号的商品列表
      const updatedProducts = [...products, newProduct]
      update(accountIndex, { accountId, products: updatedProducts })
    } else {
      // 为新账号添加商品配置
      append({ accountId, products: [newProduct] })
    }

    // 展开该账号
    setExpandedAccounts(prev => new Set(prev).add(accountId))
  }

  // 更新指定账号的商品
  const updateAccountProduct = (accountId: string, productIndex: number, field: 'title' | 'url', value: string) => {
    const { accountIndex, products } = getAccountProductsInfo(accountId)

    if (accountIndex >= 0) {
      const updatedProducts = [...products]
      updatedProducts[productIndex] = { ...updatedProducts[productIndex], [field]: value }
      update(accountIndex, { accountId, products: updatedProducts })
    }
  }

  // 删除指定账号的商品
  const removeProductFromAccount = (accountId: string, productIndex: number) => {
    const { accountIndex, products } = getAccountProductsInfo(accountId)

    if (accountIndex >= 0) {
      const updatedProducts = products.filter((_, index) => index !== productIndex)

      if (updatedProducts.length === 0) {
        // 如果没有商品了，移除整个账号配置
        remove(accountIndex)
      } else {
        // 更新商品列表
        update(accountIndex, { accountId, products: updatedProducts })
      }
    }
  }

  // 切换账号展开状态
  const toggleAccountExpanded = (accountId: string) => {
    setExpandedAccounts(prev => {
      const newSet = new Set(prev)
      if (newSet.has(accountId)) {
        newSet.delete(accountId)
      } else {
        newSet.add(accountId)
      }
      return newSet
    })
  }

  // 获取指定账号的商品列表（用于渲染）
  const getAccountProducts = (accountId: string): AccountProductProduct[] => {
    const { products } = getAccountProductsInfo(accountId)
    return products
  }

  return (
    <Card className="bg-background/70">
      <CardHeader>
        <CardTitle>购物车信息</CardTitle>
        <CardDescription>
          为每个账号配置商品信息。每个账号可以配置多个商品，也可以不配置商品。
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 账号商品配置 */}
        <div className="space-y-3">
          <Label className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            账号商品配置
          </Label>
          {selectedAccounts.length > 0 ? (
            <div className="space-y-4">
              {selectedAccounts.map(account => {
                const accountId = account.id.toString()
                const products = getAccountProducts(accountId)
                const isExpanded = expandedAccounts.has(accountId)

                return (
                  <div key={account.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={account.avatar} alt={account.nickname} />
                          <AvatarFallback className="text-xs">
                            {account.nickname?.charAt(0) || account.orgNickname?.charAt(0) || '?'}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">
                          {account.nickname || account.orgNickname}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          ({products.length} 个商品)
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          type="button"
                          size="sm"
                          variant="outline"
                          onClick={() => addProductToAccount(accountId)}
                        >
                          <Plus className="w-4 h-4 mr-1" />
                          添加商品
                        </Button>
                        {products.length > 0 && (
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={() => toggleAccountExpanded(accountId)}
                          >
                            {isExpanded ? '收起' : '展开'}
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* 商品列表 */}
                    {products.length > 0 && isExpanded && (
                      <div className="space-y-3 pl-4 border-l-2 border-gray-800">
                        {products.map((product, index) => (
                          <div key={index} className="space-y-2 p-3 rounded">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">商品 {index + 1}</span>
                              <Button
                                type="button"
                                size="sm"
                                variant="ghost"
                                onClick={() => removeProductFromAccount(accountId, index)}
                              >
                                <Trash2 className="w-4 h-4 text-red-400" />
                              </Button>
                            </div>
                            <div className="flex gap-6 items-center">
                              <div className="flex flex-col gap-2">
                                <Label className="text-xs">商品标题</Label>
                                <Input
                                  placeholder="请输入商品标题"
                                  value={product.title}
                                  onChange={e => updateAccountProduct(accountId, index, 'title', e.target.value)}
                                />
                              </div>
                              <div className="flex flex-1 flex-col gap-2">
                                <Label className="text-xs whitespace-nowrap">商品链接</Label>
                                <Input
                                  placeholder="请输入商品链接"
                                  value={product.url}
                                  onChange={e => updateAccountProduct(accountId, index, 'url', e.target.value)}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-sm text-muted-foreground border rounded p-3 text-center">
              请先在上方选择账号
            </div>
          )}
          {
            errors?.accountProducts && (
              <p className="text-sm text-red-500">{(errors.accountProducts as any).message}</p>
            )
          }
        </div>
      </CardContent>
    </Card>
  )
}
