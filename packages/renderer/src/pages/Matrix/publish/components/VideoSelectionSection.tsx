import React, { useRef } from 'react'
import {  FieldErrors } from 'react-hook-form'
import {  X, Play, RotateCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { isEmpty } from 'lodash'
import { cn } from '@/components/lib/utils'
import { VideoSelectDrawer, VideoSelectDrawerRef } from '../../components/VideoSelectDrawer'
import { VideoPreviewPopover } from '../../components/VideoPreviewModal'
import { FileUploader, FileUploaderRenderProps } from '@/components/ui/file-uploader'
import { UploadModule } from '@/libs/request/upload'

export interface SimpleVideo {
  url: string
  cover?: string 
  customCover?: string 
  name?: string
}

interface VideoSelectionSectionProps {
  selectedVideos: SimpleVideo[]
  onVideoSelect: (videos: SimpleVideo[]) => void
  onRemoveVideo: (videoUrl: string) => void
  onUpdateVideoCover: (videoUrl: string, customCoverUrl: string) => void
  errors?: FieldErrors
}

const CustomUploader: React.FC<FileUploaderRenderProps> = ({
  getRootProps,
  getInputProps,
  isLoading,
}) => {
  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      {isLoading ? '上传中...' : '点击上传'}
    </div>
  )
}

const VideoCard: React.FC<{ 
  className?: string 
  children: React.ReactNode
  onClick?: () => void
}> = ({ children, className, onClick, ...rest }) => {
  return (
    <div
      {...rest}
      onClick={onClick}
      className={cn(className,
        'border-2 border-dashed border-gray-700 h-84 aspect-[9/16] bg-gray-800/50 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-blue-500/50 hover:bg-gray-800/90 transition-all duration-200')}
    >
      {children}
    </div>
  )
}

export const VideoSelectionSection: React.FC<VideoSelectionSectionProps> = ({
  selectedVideos,
  onVideoSelect,
  onRemoveVideo,
  onUpdateVideoCover,
  errors
}) => {
  const videoSelectDrawerRef = useRef<VideoSelectDrawerRef>(null)

  const handleOpenVideoSelect = () => {
    videoSelectDrawerRef.current?.open()
  }

  const handleReplaceVideo = (currentVideo: SimpleVideo) => {
    videoSelectDrawerRef.current?.open([currentVideo])
  }

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        添加视频 <span className="text-red-500">*</span>
      </Label>
      <p className="text-sm text-muted-foreground mb-4">
        视频时长≤15分钟，大小≤4GB，推荐上传720p以上分辨率，支持常见视频格式，推荐使用MP4。
      </p>

      {/* 视频选择区域 */}
      {isEmpty(selectedVideos) && (
        <div className="flex items-center gap-4 bg-gray-700/50 w-fit p-5 rounded-2xl">
          <div className="flex gap-2">
            <VideoCard onClick={handleOpenVideoSelect}>
              <span className="text-gray-600 dark:text-gray-300 text-sm">添加视频</span>
            </VideoCard>

            <VideoCard>
              <span className="text-gray-600 dark:text-gray-300 text-sm">添加封面</span>
            </VideoCard>
          </div>
        </div>
      )}
      
      <div className="flex gap-2">
        {/* 已选择的视频列表 */}
        {selectedVideos.map(video => (
          <div key={video.url} className="relative group/container flex items-center gap-4 bg-gray-700/50 w-fit p-5 rounded-2xl">
            {/* 删除按钮 */}
            <Button
              variant="ghost"
              size="sm"
              className="absolute -top-2 -right-2 z-20 h-6 w-6 p-0 bg-red-600 hover:bg-red-700 text-white rounded-full opacity-0 group-hover/container:opacity-100 transition-opacity duration-200"
              onClick={() => onRemoveVideo(video.url)}
            >
              <X className="w-3 h-3" />
            </Button>
            
            <div className="flex gap-2 items-center">
              {/* 第一个VideoCard - 视频预览卡片 */}
              <div className="relative group">
                <VideoCard className="relative overflow-hidden">
                  <div className="w-full h-full overflow-hidden">
                    {video.cover ? (
                      <img
                        src={video.cover}
                        alt="视频文件"
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-xs text-gray-400">
                        无封面
                      </div>
                    )}
                  </div>
                  
                  {/* 悬浮按钮层 */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                    <VideoPreviewPopover
                      video={video}
                      trigger={
                        <Button
                          size="sm"
                          variant="secondary"
                          className="text-xs px-3 py-1 h-auto bg-blue-600 hover:bg-blue-700 text-white border-0"
                        >
                          <Play className="w-3 h-3 mr-1" />
                          预览
                        </Button>
                      }
                    />
                    <Button
                      size="sm"
                      variant="secondary"
                      className="text-xs px-3 py-1 h-auto bg-gray-600 hover:bg-gray-700 text-white border-0"
                      onClick={() => handleReplaceVideo(video)}
                      type="button"

                    >
                      <RotateCcw className="w-3 h-3 mr-1" />
                      更换视频
                    </Button>
                  </div>
                  
                  <div className="text-xs text-muted px-2 py-1 w-full bg-gray-800/80">
                    {video?.name ?? '暂无名称'}
                  </div>
                </VideoCard>
              </div>

              {/* 第二个VideoCard - 封面卡片 */}
              <div className="relative group">
                <VideoCard className="relative overflow-hidden">
                  <div className="w-full h-full overflow-hidden">
                    {video.customCover || video.cover ? (
                      <img
                        src={video.customCover || video.cover}
                        alt="视频封面"
                        className="w-full h-full object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-xs text-gray-400">
                        无封面
                      </div>
                    )}
                  </div>

                  {/* 悬浮按钮层 */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center gap-2">
                    <FileUploader
                      fileTypes={['image/*']}
                      module={UploadModule.publishing}
                      maxFiles={1}
                      folderUuid=""
                      onUpload={files => {
                        if (files.length > 0 && files[0].url) {
                          onUpdateVideoCover(video.url, files[0].url)
                        }
                      }}
                      renderCustomComponent={props => <CustomUploader {...props} />}
                    />
                  </div>

                  <div className="text-xs text-muted px-2 py-1 w-full bg-gray-800/80">
                    {video?.name ?? '暂无名称'}
                  </div>
                </VideoCard>
              </div>
            </div>
          </div>
        ))}
      </div>

      {errors?.videoList && (
        <p className="text-sm text-red-500">{(errors.videoList as any).message}</p>
      )}

      {/* 视频选择抽屉 */}
      <VideoSelectDrawer
        ref={videoSelectDrawerRef}
        onVideoSelect={onVideoSelect}
      />
    </div>
  )
}
