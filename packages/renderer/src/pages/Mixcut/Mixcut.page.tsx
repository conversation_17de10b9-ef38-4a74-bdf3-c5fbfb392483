import React, { <PERSON><PERSON><PERSON><PERSON><PERSON>hildren, useEffect, useMemo, useRef, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Grid3X3, List, MoreHorizontal, Plus, Search, Settings, X, Play, Check } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger
} from '@/components/ui/drawer.tsx'
import { useVirtualTab } from '@/contexts/virtual-tab.context.tsx'
import useVirtualTabsStore from '@/stores/useVirtualTabsStore.ts'
import { GeneratedMixcut, MixcutProvider, MultiSelection, useMixcutContext } from '@/contexts/mixcut.context.tsx'
import { BatchUploadOverlay } from '@/components/BatchUploadOverlay'
import { useQuery } from '@tanstack/react-query'
import { clsx } from 'clsx'
import { Player, PlayerRef } from '@remotion/player'
import { Renderer } from '@rve/renderer'
import { loadProjectState } from '@rve/editor/utils/project-state-storage.ts'
import { OverlayType, VideoOverlay } from '@app/rve-shared/types'
import { useQueryVideoKeyframe } from '@/hooks/queries/useQueryVideoKeyframe.ts'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'
import { EditorModule } from '@/libs/request/api/editor.ts'
import { Mixcut } from '@/types/mixcut.ts'

// 顶部 - 标签栏组件
const PreviewTabBar = () => {
  const { activeTab, setActiveTab } = useMixcutContext()

  return (
    <div className="border-b border-border bg-background">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="h-12 w-full justify-start rounded-none border-0 bg-transparent p-0">
          <TabsTrigger
            value="generation"
            className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            生成混剪
          </TabsTrigger>
          <TabsTrigger
            value="saved"
            className="h-12 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            我保存的混剪
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  )
}

// 工具栏组件
const PreviewToolbar = () => {
  const [viewMode, setViewMode] = useState('grid')
  const {
    // activeTab,
    generation: { selectedIndices, generateCombinations, uploadSelectedPreviews }
  } = useMixcutContext()

  return (
    <div className="flex items-center justify-between p-4 border-b border-border bg-background">
      <div className="flex items-center space-x-4">
        {/* 视图切换按钮 */}
        <div className="flex items-center space-x-1">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('grid')}
            className="h-8 w-8 p-0"
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="h-8 w-8 p-0"
          >
            <List className="h-4 w-4" />
          </Button>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="搜索视频..."
            className="pl-9 w-64"
          />
        </div>

        <Select defaultValue="invisible">
          <SelectTrigger className="w-40">
            <SelectValue placeholder="" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="invisible">当前脚本内去重</SelectItem>
            <SelectItem value="visible">当前项目内去重</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="basic">
          <SelectTrigger className="w-40">
            <SelectValue placeholder="" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="basic">严谨去重算法</SelectItem>
            <SelectItem value="advanced">标准去重算法</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        {/* 混剪规则 Drawer */}
        <MixcutRulesDrawer>
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-2" />
            混剪规则
          </Button>
        </MixcutRulesDrawer>

        <Button variant="default" size="sm" onClick={generateCombinations}>
          生成混剪预览
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={uploadSelectedPreviews}
          disabled={selectedIndices.size === 0}
        >
          保存所选混剪 ({selectedIndices.size})
        </Button>
      </div>
    </div>
  )
}

const VideoPreviewFrame: React.FC<{ overlay?: VideoOverlay }> = ({ overlay }) => {
  const { data: keyframe, isLoading: keyframeLoading } = useQueryVideoKeyframe(overlay?.src)

  return (
    <>
      {keyframe && overlay ? (
        <img
          src={keyframe}
          alt={`混剪预览${overlay.src}`}
          className="w-full h-full object-cover rounded-sm"
          onError={e => {
            // 图片加载失败时显示默认背景
            e.currentTarget.style.display = 'none'
          }}
        />
      ) : (
        <div className="w-full h-full bg-gray-500 flex items-center justify-center">
          {keyframeLoading ? (
            <div className="text-white/60 text-sm">加载中...</div>
          ) : (
            <div className="text-white/60 text-sm">无预览</div>
          )}
        </div>
      )}
    </>
  )
}

const MultiSelectableCard: React.FC<PropsWithChildren<MultiSelection & { index: number }>> = ({
  selectedIndices, toggleSelection, children, index
}) => {
  const isSelected = selectedIndices.has(index)

  return (
    <div
      className={clsx(
        'relative rounded-sm',
        isSelected ? 'outline-primary outline-2' : 'outline-transparent',
      )}
      onClick={() => toggleSelection(index)}
    >
      {/* 选中状态勾选图标 */}
      {isSelected && (
        <div className="z-9 absolute top-2 left-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
          <Check className="w-4 h-4 text-white" />
        </div>
      )}

      {children}
    </div>
  )
}

const MixcutPreviewCard: React.FC<GeneratedMixcut & { index: number }> = ({ index: i, ...combo }) => {
  const { generation, state } = useMixcutContext()

  const firstVideoOverlay = useMemo(() => {
    const { tracks } = state
    // 获取第一个分镜对应的视频轨道索引
    const firstStoryboardTrackIndex = combo.combination[0]

    // 检查轨道索引是否有效
    if (firstStoryboardTrackIndex >= tracks.length) {
      console.warn(`视频轨道索引 ${firstStoryboardTrackIndex} 超出范围，总轨道数: ${tracks.length}`)
      return undefined
    }

    // 获取对应的视频轨道
    const targetVideoTrack = tracks[firstStoryboardTrackIndex]
    if (!targetVideoTrack || !targetVideoTrack.overlays.length) {
      console.warn(`视频轨道 ${firstStoryboardTrackIndex} 不存在或没有视频素材`)
      return undefined
    }

    // 获取轨道中第一个视频 overlay
    const firstVideoOverlay = targetVideoTrack.overlays.find(
      overlay => overlay.storyboardIndex === 0 && overlay.type === OverlayType.VIDEO
    ) as VideoOverlay | null

    if (!firstVideoOverlay || !firstVideoOverlay.src) {
      console.warn(`视频轨道 ${firstStoryboardTrackIndex} 中没有找到有效的视频素材`)
      return undefined
    }

    return firstVideoOverlay
  }, [state.tracks])

  return (
    <MultiSelectableCard {...generation} index={i}>
      <div
        className={clsx(
          'w-48 h-64 relative rounded-sm outline-3 cursor-pointer group',
        )}
      >
        {/* 预览图片背景 */}
        <VideoPreviewFrame overlay={firstVideoOverlay} />

        {/* 重复率标签 */}
        <div className="absolute right-[-8px] top-[-8px] bg-black/70 text-white p-1 text-xs rounded">
          重复率{(combo.similarity * 100).toFixed(1)}%
        </div>

        {/* 播放按钮 - 悬浮时显示在右下角 */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            size="sm"
            variant="secondary"
            className="h-8 w-8 p-0 bg-black/70 hover:bg-black/90 border-0"
            onClick={e => {
              e.stopPropagation()
              generation.setActiveIndex(i)
            }}
          >
            <Play className="w-4 h-4 text-white" />
          </Button>
        </div>
      </div>
    </MultiSelectableCard>

  )
}

const SavedMixcutCard: React.FC<Mixcut.SavedMixcut & { index: number }> = ({ index: i, ...mixcut }) => {
  const { saved } = useMixcutContext()

  return (
    <MultiSelectableCard {...saved} index={i}>
      <div
        className={clsx(
          'w-48 h-64 relative outline-3 cursor-pointer ',
        )}
      >
        {/* 预览图片背景 */}
        <img
          src={mixcut.cover}
          alt={`saved-mixcut-${mixcut.id}`}
          className="w-full h-full object-cover rounded-sm"
          onError={e => {
          // 图片加载失败时显示默认背景
            e.currentTarget.style.display = 'none'
          }}
        />

        {/* 重复率标签 */}
        <div className="absolute right-[-8px] top-[-8px] bg-black/70 text-white p-1 text-xs rounded">
          重复率{(mixcut.repetitionRate * 100).toFixed(1)}%
        </div>
      </div>
    </MultiSelectableCard>
  )
}

// 左侧① - 混剪预览列表
const MixcutPreviewListPanel = () => {
  const { generation: { generatedMixcuts } } = useMixcutContext()

  if (generatedMixcuts.length) {
    return (
      <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
        {generatedMixcuts.map((combo, i) => (
          <MixcutPreviewCard
            {...combo}
            key={combo.combination.join(',')}
            index={i}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 bg-background">
      {/* 空状态插图 */}
      <div className="mb-6">
        <div className="relative">
          {/* 简化的插图 - 人物和列表 */}
          <div className="w-32 h-32 bg-muted rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/20 rounded-full mx-auto mb-2 flex items-center justify-center">
                <List className="w-8 h-8 text-primary" />
              </div>
              <div className="space-y-1">
                <div className="w-12 h-1 bg-primary/30 rounded mx-auto" />
                <div className="w-8 h-1 bg-primary/20 rounded mx-auto" />
                <div className="w-10 h-1 bg-primary/20 rounded mx-auto" />
              </div>
            </div>
          </div>

          {/* 添加按钮 */}
          <div className="absolute -bottom-2 -right-2">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center shadow-lg">
              <Plus className="w-4 h-4 text-primary-foreground" />
            </div>
          </div>
        </div>
      </div>

      {/* 空状态文本 */}
      <div className="text-center mb-6">
        <p className="text-muted-foreground mb-2">
          未保存视频，请到混剪预览列表中保存视频
        </p>
      </div>

      {/* 添加视频按钮 */}
      <Button className="bg-primary hover:bg-primary/90">
        去生成混剪
      </Button>
    </div>
  )
}

// 左侧② - 混剪预览列表
const SavedMixcutListPanel = () => {
  const { params } = useVirtualTab()
  const { scriptId } = params || {}
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: () => EditorModule.listMixcuts(scriptId)
  })

  return (
    <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
      {data?.list.map((combo, i) => (
        <SavedMixcutCard
          {...combo}
          key={i}
          index={i}
        />
      ))}
    </div>
  )
}

// 中右 - 混剪结果分镜预览
const StoryboardPanel: React.FC = () => {
  const { playerOverlays, generation: { activeItem } } = useMixcutContext()

  const items = useMemo(() => {
    return playerOverlays
      .filter(o => o.type === OverlayType.VIDEO && typeof o.storyboardIndex === 'number')
      .sort((a, b) => a.storyboardIndex! - b.storyboardIndex!)
  }, [playerOverlays])

  if (!items.length || !activeItem) return null

  return (
    <div className="border-l border-border px-4 pt-8">
      <div className="pb-3">分镜视频预览</div>
      <div className="flex flex-col gap-3 overflow-y-auto">
        {items.map(item => (
          <div key={item.id} >
            <div className="w-32 h-48 bg-gray-500 rounded-sm">
              <VideoPreviewFrame overlay={item as VideoOverlay} />
            </div>
            <div className="text-sm text-gray-400 mt-1.5">
              分镜{item.storyboardIndex! + 1} - 轨道{activeItem.combination[item.storyboardIndex!]}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// 右侧 - 视频预览面板组件
const VideoPreviewPanel = () => {
  const { playerOverlays, state: { playerMetadata } } = useMixcutContext()

  const playerRef = useRef<PlayerRef | null>(null)

  const { width, height, fps, durationInFrames } = playerMetadata

  useEffect(() => {
    const player = playerRef.current

    if (player) {
      player.seekTo(0)
      player.play()
    }
  }, [playerOverlays])

  return (
    <div className="w-[30vw] border-l border-border bg-background flex flex-col">
      {/* 预览标题 */}
      <div className="p-4 border-b border-border">
        <h3 className="text-sm font-medium text-foreground flex items-center">
          预览窗口
          <Button variant="ghost" size="sm" className="ml-auto h-6 w-6 p-0">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </h3>
      </div>

      {/* 视频播放器区域 */}
      <div className="flex-1 bg-black relative px-4">
        {
          playerOverlays.length
            ? (
              <Player
                ref={playerRef}
                loop={false}
                component={Renderer}
                compositionWidth={width}
                compositionHeight={height}
                style={{
                  width: '100%',
                  height: '100%',
                }}
                fps={fps}
                durationInFrames={durationInFrames}
                inputProps={{
                  overlays: playerOverlays,
                  playerMetadata
                } as any}
              />
            )
            : (
              // 视频播放器占位
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-white/60 text-sm text-center">在左侧选择一个结果后 <br />可以在此处预览完整视频</div>
              </div>
            )
        }
      </div>
    </div>
  )
}

// 主内容区域组件
const PreviewMainContent = () => {
  const { activeTab } = useMixcutContext()

  return (
    <div className="flex flex-1 overflow-hidden">
      {activeTab === 'generation' ? <MixcutPreviewListPanel /> : <SavedMixcutListPanel />}
      <StoryboardPanel />
      <VideoPreviewPanel />
    </div>
  )
}

// 混剪规则 Tab 组件
const MixcutRulesTabs = () => {
  const [activeTab, setActiveTab] = useState('material-settings')

  const tabs = [
    { value: 'material-settings', label: '混剪素材设置' },
    { value: 'video-dedup', label: '视频智能去重' },
    { value: 'background-music', label: '背景音乐设置' },
    { value: 'subtitle-style', label: '随机字幕样式设置' },
    { value: 'text-group-style', label: '随机文字组样式设置' },
    { value: 'voice-style', label: '随机口播音色设置' },
    { value: 'background-style', label: '随机背景设置' },
    { value: 'effect-style', label: '随机特效设置' },
  ]

  return (
    <div className="flex h-full">
      {/* 左侧垂直 Tab 导航 */}
      <div className="w-48 border-r border-border bg-muted/30">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          orientation="vertical"
          className="h-full"
        >
          <TabsList className="flex flex-col h-full w-full justify-start bg-transparent p-2 space-y-1">
            {tabs.map(tab => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="w-full justify-start text-left px-3 py-2 text-sm font-normal
                  data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm
                  hover:bg-background/50 transition-colors"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* 垂直分割线 */}
      <Separator orientation="vertical" className="h-full" />

      {/* 右侧内容区域 */}
      <div className="flex-1 p-6">
        <Tabs value={activeTab} className="h-full">
          {tabs.map(tab => (
            <TabsContent key={tab.value} value={tab.value} className="h-full">
              <div className="flex items-center justify-center h-full text-muted-foreground">
                {tab.label} 内容区域
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}

// 混剪规则 Drawer 组件
const MixcutRulesDrawer: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <Drawer  direction="right" >
      <DrawerTrigger asChild>
        {children}
      </DrawerTrigger>

      <DrawerContent className="max-w-[640px] p-0 flex flex-col">
        {/* Drawer 标题栏 */}
        <DrawerHeader className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <DrawerTitle className="text-lg font-medium">
              混剪规则设置
            </DrawerTitle>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        {/* Drawer 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <MixcutRulesTabs />
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default function MixcutPage() {
  const { params, id } = useVirtualTab()
  const scriptId = params?.scriptId
  const { closeTab } = useVirtualTabsStore()

  const { data: state = null, isLoading } = useQuery({
    queryKey: ['PROJECT_STATE', scriptId],
    queryFn: () => loadProjectState(scriptId, true),
  })

  if (!scriptId || !state) {
    if (isLoading) return <>Loading...</>

    closeTab(id)
    return null
  }

  return (
    <MixcutProvider state={state}>
      <div className="flex flex-col h-screen bg-background">
        <PreviewTabBar />
        <PreviewToolbar />
        <PreviewMainContent />
      </div>

      {/* 批量上传进度遮罩 */}
      <BatchUploadOverlay />
    </MixcutProvider>
  )
}
