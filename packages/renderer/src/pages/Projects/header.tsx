import { cn } from '@/components/lib/utils'
import React from 'react'
import { NavLink } from 'react-router'

type Route = {
  path: string
  title: string
}

const paths: Route[] = [
  { path: 'creative', title: '我的创意' },
  { path: 'material', title: '我的素材' },
  { path: 'works', title: '我的作品' },
  { path: 'recycle-bin', title: '回收站' },
]

export function Header() {
  return (
    <div className="pt-2 flex pl-4">
      <div className="flex bg-background/50 p-1 gap-4 rounded-lg inset-shadow-muted-foreground">
        {paths.map(({ path, title }) => (
          <NavLink
            key={path}
            to={path}
            className={({ isActive }) => {
              return cn(
                'text-secondary-foreground px-4 py-1 rounded-md',
                isActive && 'bg-linear-to-b from-muted-foreground/70 to-muted-foreground/40',
                // isActive && 'border-b-3 border-primary text-primary',
              )
            }}
          >
            {title}
          </NavLink>
        ))}
      </div>
    </div>
  )
}
