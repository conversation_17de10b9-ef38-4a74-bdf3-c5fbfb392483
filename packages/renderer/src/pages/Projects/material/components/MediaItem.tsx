import React, { useState, useEffect } from 'react'
import {
  EllipsisVertical,
} from 'lucide-react'
import { MaterialResource } from '@/types/resources'
import folderIcon from '@/assets/folder.svg'
import { cn } from '@/components/lib/utils'
import { ResourceModule } from '@/libs/request/api/resource'

interface MediaItemProps {
  orientation: string
  media: MaterialResource.Media
  isSelected: boolean
  isFolder: boolean
  currentFolderId?: string
  isTrash?: boolean
  actions: FolderAction[] | MediaAction[]
  onToggleSelect: (fileId: string) => void
}

type FolderActionHandler = (nodeId: string, parentId?: string, label?: string) => void
export type FolderAction = {
  icon: React.ReactNode
  label: string
  onClick: FolderActionHandler
}

type MediaActionHandler = (fileId: string, fileName: string, folderUuid: string) => void
export type MediaAction = {
  icon: React.ReactNode
  label: string
  onClick: MediaActionHandler
}

const MediaItemComponent: React.FC<MediaItemProps> = ({
  orientation,
  media,
  isSelected,
  isFolder,
  currentFolderId,
  isTrash = false,
  actions,
  onToggleSelect,
}) => {
  const [popup, setPopup] = useState(false)
  const [coverUrl, setCoverUrl] = useState<string>('#')

  useEffect(() => {
    const fetchCover = async () => {
      if (!isFolder && media.cover) {
        try {
          const res = await ResourceModule.media.cover(media.cover)
          const parser = new DOMParser()
          const doc = parser.parseFromString(res, 'text/html')
          const anchor = doc.querySelector('a')
          setCoverUrl(anchor?.href ?? '#')
        } catch (error) {
          console.error('获取封面地址失败:', error)
        }
      }
    }

    fetchCover()
  }, [media.cover, media.resType])

  return (
    <div
      key={media.fileId}
      className={cn('flex flex-col relative group', orientation === 'horizontal' ? 'w-50' : 'w-40')}
    >
      {/* 类型图标和选择框 */}
      <div
        className={cn(
          'w-full border rounded-sm shadow-sm flex flex-col relative group',
          orientation === 'horizontal' ? 'h-50' : 'h-64',
        )}
      >
        {isTrash && (
          <div className="absolute top-2 left-2 text-xs text-gray-500 bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
            剩余xx天
          </div>
        )}
        {!isFolder && !isTrash && (
          <div className="absolute top-2 left-2 text-xs text-gray-500 bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
            {media.resType !== MaterialResource.MediaType.AUDIO && `合成使用${media.useCount}次`}
          </div>
        )}

        <div className="absolute top-2 right-2 z-3">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onToggleSelect(media.fileId)}
            className="w-4 h-4 cursor-pointer accent-primary-highlight1"
          />
        </div>
        <div className="flex h-64 items-center justify-center relative">
          <div className="flex items-center space-x-1 overflow-hidden">
            {media.resType === 0 && <img src={folderIcon} alt="文件夹" className="w-230 h-25" />}
            {media.resType === MaterialResource.MediaType.VIDEO && (
              <img
                src={coverUrl}
                alt={media.fileName}
                className="w-full h-auto object-cover max-w-full rounded"
              />
            )}
            {media.resType === MaterialResource.MediaType.AUDIO && (
              <img
                src={coverUrl}
                alt={media.fileName}
                className="w-full h-auto object-cover max-w-full"
              />
            )}
            {media.resType === MaterialResource.MediaType.IMAGE && (
              <img
                src={coverUrl}
                alt={media.fileName}
                className="w-full h-auto object-cover max-w-full"
              />
            )}
          </div>
          <div className="absolute bottom-1 left-2 text-xs text-gray-500">
            {media.resType === 0 ? (
              <div className="text-xs bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                {media.childrenFolder}个文件夹，{media.mediaNum}个素材
              </div>
            ) : (
              <div className="text-xs bg-gray-100 rounded px-1 py-0.5 mb-1 inline-block">
                {media.resType === MaterialResource.MediaType.IMAGE
                  ? `${media.fileSize}MB`
                  : `${media.duration}/${media.fileSize}MB`}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 信息 */}
      <div className="flex-1 flex flex-col justify-end py-2">
        <div className="text-sm font-medium truncate mb-1">{media.fileName}</div>
        <div className="text-xs text-gray-400">
          {media.createTime ? new Date(media.createTime).toLocaleTimeString() : ''}
        </div>
      </div>
      {/* 右下角更多操作 */}
      <div className="absolute bottom-2 right-0 text-sm">
        <button
          className=""
          onMouseEnter={() => setPopup(true)}
          onMouseLeave={() => setPopup(false)}
        >
          <div className="hover:bg-gray-100 dark:hover:bg-neutral-700 rounded p-1">
            <EllipsisVertical className="w-6 h-6 text-gray-500" />
          </div>

          {popup && (
            <div className="absolute right-0 top-4 z-10 pt-4">
              <div className="border dark:bg-neutral-800 rounded shadow-lg min-w-[150px] py-2 px-4">
                {actions
                  .filter(action => action.label !== '新建文件夹')
                  .map((action, idx) => (
                    <div
                      key={idx}
                      className="flex items-center px-3 py-1 cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700"
                      onClick={e => {
                        e.stopPropagation()
                        if (isFolder) {
                          action.onClick?.(media.fileId, currentFolderId, media.fileName)
                        } else {
                          action.onClick?.(media.fileId, media.fileName, media.folderUuid)
                        }
                      }}
                    >
                      {action.icon}
                      <span className="ml-2">{action.label}</span>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </button>
      </div>
    </div>
  )
}
const MediaItem = React.memo(MediaItemComponent)
export default MediaItem
