import React, { useState, useEffect, useMemo } from 'react'
import { useQueries } from '@tanstack/react-query'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { useInfiniteQueryProjectList } from '@/hooks/queries/useQueryProject'
import { fetchMaterialDirectoryList } from '@/hooks/queries/useQueryMaterial'
import TreeList, { TreeNode } from '@/components/TreeList'
import { Plus } from 'lucide-react'
import { SearchInput } from '@/components/ui/search-input'
import { useItemActions } from '@/hooks/useItemActions'
import { ResourceSource } from '@/types/resources'

interface MoveDialogProps {
  open: boolean
  moveId: string
  moveType: ResourceSource
  dirList?: TreeNode[]
  onOpenChange: (open: boolean) => void
  onConfirm: (selectedNode: any) => void
}

const MoveDialog: React.FC<MoveDialogProps> = ({
  open,
  moveId,
  dirList,
  moveType = ResourceSource.FOLDER,
  onOpenChange,
  onConfirm,
}) => {
  const [keyword, setKeyword] = useState('')
  const [selectedNode, setSelectedNode] = useState<any>(null)
  const { createItem, moveItem } = useItemActions()

  const { data: projectData } = useInfiniteQueryProjectList({})
  const projects = useMemo(() => projectData?.pages.flatMap(page => page.list) || [], [projectData])

  // 针对每个项目请求目录树
  const projectTrees = useQueries({
    queries: projects.map(project => ({
      queryKey: ['materialDirectoryList', project.id, keyword],
      queryFn: () => fetchMaterialDirectoryList({ projectId: Number(project.id), keyword }),
      enabled: !!projects.length,
    })),
  })

  /** 拼接为项目+目录的树结构 */
  const combinedTreeData = useMemo(() => {
    return projects.map((project, index) => ({
      id: `project-${project.id}`,
      label: project.projectName,
      type: 'project', // 用于区分项目节点
      children: projectTrees[index]?.data || [],
    }))
  }, [projects, projectTrees])

  const treeData = useMemo(() => {
    //如果是本地资源，直接使用传入的dirList
    if (
      moveType === ResourceSource.LOCAL_STICK ||
      moveType === ResourceSource.LOCAL_STICK_FOLDER ||
      moveType === ResourceSource.LOCAL_MUSIC ||
      moveType === ResourceSource.LOCAL_MUSIC_FOLDER ||
      moveType === ResourceSource.LOCAL_SOUND ||
      moveType === ResourceSource.LOCAL_SOUND_FOLDER
    ) {
      return dirList || []
    }
    return combinedTreeData
  }, [moveType, dirList, combinedTreeData])

  const handleConfirm = async () => {
    if (!selectedNode || selectedNode.type === 'project') return

    if (moveType !== ResourceSource.MULTI_SELECT) {
      await moveItem(moveType, moveId, selectedNode.id)
    }

    onConfirm?.(selectedNode)
    onOpenChange(false)
  }

  const actions = [
    {
      icon: <Plus className="w-4 h-4" />,
      label: '新建',
      onClick: (nodeId: string) => {
        createItem(moveType, nodeId, {
          label: '文件夹名称',
          headerTitle: '文件夹',
        })
      },
    },
  ]

  useEffect(() => {
    if (!open) {
      setKeyword('')
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>移动</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto py-4">
          <SearchInput
            placeholder="关键词搜索"
            value={keyword}
            onChange={e => setKeyword(e.target.value)}
            size="lg"
            containerClassName="mx-4"
          />
          <TreeList
            data={treeData}
            className="w-full flex-1 max-h-[500px] overflow-auto"
            selectStyle="bg-primary-highlight1"
            actions={actions}
            showEllipsis={false}
            keyword={keyword}
            onSelect={node => {
              setSelectedNode(node)
            }}
          />
        </div>

        <DialogFooter>
          <button onClick={() => onOpenChange(false)} className="px-4 py-2 bg-primary/20 rounded-md">
            取消
          </button>
          <button onClick={handleConfirm} className="px-4 py-2 bg-primary-highlight1 text-white rounded-md">
            确定
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default MoveDialog
