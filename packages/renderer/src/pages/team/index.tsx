import React, { useMemo, useState } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { useCurrentTeam, usePaginationTeamMembers, useTeamRoles } from '@/hooks/queries/useQueryTeam'
import { Separator } from '@radix-ui/react-dropdown-menu'
import { DataTable } from '@/components/ui/data-table'
import { Member, TeamAPI, TeamPermission } from '@/libs/request/api/team'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { ColumnDef, Row } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { CheckIcon, Ellipsis, Minus } from 'lucide-react'
import { Checkbox, CheckboxIndicator } from '@radix-ui/react-checkbox'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

export default function TeamPage() {
  const { data: team } = useCurrentTeam()
  const { data: members, setPagination, pagination, refetch } = usePaginationTeamMembers()
  const { data: roles } = useTeamRoles()
  const [selected, setSelected] = useState<number[]>([])

  const switchColumn = (field: keyof TeamPermission, title: string): ColumnDef<Member> => ({
    accessorKey: field,
    header: title,
    cell: ({ row }: { row: Row<Member> }) => {
      return (
        <div className="flex items-center">
          <Switch
            checked={row.original[field]}
            onCheckedChange={async checked => {
              await TeamAPI.setMemberPermission({
                memberId: row.original.id,
                type: field,
                status: checked,
              })
              refetch()
            }}
          />
        </div>
      )
    },
  })

  const checked = useMemo(() => {
    const set = new Set(selected)
    const a = members.some(member => set.has(member.id))
    const b = members.some(member => !set.has(member.id))
    return a && b ? 'indeterminate' : a
  }, [selected, members])

  return (
    <div className="flex flex-col items-stretch h-full p-4">
      <div className="p-4 w-full flex items-center justify-between">
        <div className="flex items-center gap-2">
          {team ? <h4 className="text-lg font-bold">{team.name}</h4> : <Skeleton className="h-8 w-48" />}
        </div>
        <div className="flex items-center gap-2">
          <div className="text-sm text-muted-foreground/70">
            已选择：{selected.length}/{members.length}
          </div>
        </div>
      </div>
      <Separator className="my-2" />
      <DataTable<Member, unknown>
        columns={[
          {
            id: 'select',
            header: () => (
              <div>
                <Checkbox
                  checked={checked}
                  onClick={() => {
                    if (checked) setSelected([])
                    else setSelected(members.map(member => member.id))
                  }}
                  className="size-4 border rounded flex items-center justify-center bg-muted-foreground/30"
                >
                  {checked === true && (
                    <CheckboxIndicator>
                      <CheckIcon className="size-4" />
                    </CheckboxIndicator>
                  )}
                  {checked === 'indeterminate' && (
                    <CheckboxIndicator>
                      <Minus className="size-4" />
                    </CheckboxIndicator>
                  )}
                </Checkbox>
              </div>
            ),
            cell: ({ row }) => {
              const checked = selected.includes(row.original.id)
              return (
                <div>
                  <Checkbox
                    checked={checked}
                    onClick={() => {
                      if (checked) setSelected([])
                      else setSelected(members.map(member => member.id))
                    }}
                    className="size-4 border rounded flex items-center justify-center bg-muted-foreground/30"
                  >
                    {checked && (
                      <CheckboxIndicator>
                        <CheckIcon className="size-4" />
                      </CheckboxIndicator>
                    )}
                  </Checkbox>
                </div>
              )
            },
          },
          {
            accessorKey: 'nickname',
            header: '姓名',
            cell: ({ row }) => (
              <>
                <span className="text-sm">{row.original.nickname}</span>
                {row.original.isCreator && (
                  <Badge className="ml-2" variant="secondary">
                    我
                  </Badge>
                )}
              </>
            ),
          },
          {
            accessorKey: 'mobile',
            header: '手机号',
            cell: ({ row }) => <span className="text-sm">{row.original.mobile}</span>,
          },
          switchColumn('isEditVideo', '混剪权限'),
          switchColumn('isPublishVideo', '发布权限'),
          switchColumn('isComment', '抖音评论权限'),
          switchColumn('isPrivateMsg', '私信管理权限'),
          switchColumn('isTiktokComment', 'TikTok 评论权限'),
          {
            accessorKey: 'remark',
            header: '备注',
            cell: ({ row }) => <span className="text-sm">{row.original.remark}</span>,
          },
          {
            header: '操作',
            cell: ({ row }) => {
              return (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Ellipsis className="size-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>转移权限</DropdownMenuItem>
                    <DropdownMenuItem>删除用户</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )
            },
          },
        ]}
        data={members || []}
        pagination={pagination}
        onPaginationChange={setPagination}
      />
    </div>
  )
}
