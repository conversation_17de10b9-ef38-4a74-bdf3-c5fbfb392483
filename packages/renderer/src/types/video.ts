import { PaginationParams } from "@app/shared/types/database.types";

export interface RemixPageParams extends PaginationParams {
  repetitionRateRange?: string;
  scriptId?: string;
  sortField?: string;
  sortOrder?: string;
}


export interface Remix {
  contentOid: string;
  cover: string;
  createAt: null;
  createTime: number;
  duration: number;
  id: number;
  name: string;
  product: number;
  projectId: number;
  repetitionRate: number;
  scriptId: number;
  updateAt: null;
}

export interface CreativeVideoRequestParams extends PaginationParams {
  associated?: string;
  auditStatus?: string;
  composeStatus?: string;
  createAt?: string[];
  distStatus?: string;
  hadDownload?: string;
  keyword?: string;
  product?: string;
  projectId?: string;
  repetitionRateRange?: string;
  scriptId?: string;
  sortField?: string;
  sortOrder?: string;
}


export interface CreativeVideo {
  id: number
  scriptId: number
  name: string
  cover: string
  duration: number
  size: number
  repetitionRate: number
  auditStatus: number
  composeStatus: number
  composeSubStatus: number
  composeReason: string
  url: string
  previewFile: string
  commentCount: number
  downloadCount: number
  product: number
  digitalPersonModelId: number
  vid: number
  cid: number
  distList: string
  projectId: number
  createTime: number
}