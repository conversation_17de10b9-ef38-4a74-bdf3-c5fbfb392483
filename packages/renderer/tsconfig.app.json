{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": false,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react",

    /* Linting */
    "strict": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,
    "strictNullChecks": true,

    /* Path Aliases */
    "baseUrl": ".",
    "paths": {
      "@app/shared/*": ["../shared/*"],
      "@app/rve-shared/*": ["../rve-shared/*"],
      "@/*": ["src/*"],
      "@rve/editor/*": [
        "./src/modules/video-editor/components/editor/version-7.0.0/*"
      ],
      "@rve/renderer": [
        "./src/modules/video-editor/components/renderer/index.ts"
      ],
      "@rve/renderer/*": [
        "./src/modules/video-editor/components/renderer/*"
      ],
    }
  },
  "include": [
    "src"
  ]
}
