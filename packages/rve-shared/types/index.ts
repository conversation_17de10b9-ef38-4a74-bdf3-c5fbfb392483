import { z } from 'zod'

export enum TrackType {
  /**
   * 分镜轨道
   */
  STORYBOARD = 'storyboard',

  /**
   * 视频轨道
   */
  VIDEO = 'video',

  /**
   * 口播轨道
   */
  NARRATION = 'narration',

  /**
   * 声音轨道
   */
  SOUND = 'sound',

  /**
   * 文字轨道
   */
  TEXT = 'text',

  /**
   * 图片轨道.
   */
  IMAGE = 'image',

  /**
   * 混合轨道. 可以存放除分镜外的任意类型
   */
  MIXED = 'mixed',

  // /**
  //  * 占位符. 预留设计, 作用暂定
  //  */
  // EMPTY = 'EMPTY',
}

export type Track = {
  /**
   * 轨道类型. 不同类型的轨道接受不同类型的 overlay
   */
  type: TrackType

  /**
   * 轨道中包含的 overlay
   */
  overlays: Overlay[]

  /**
   * 是否为全局轨道. 为假值时则说明是分镜轨道, 将参与混剪, 且其内的 Overlay 受到长度限制
   */
  isGlobalTrack?: boolean
}

// Define overlay types enum
// TODO: duplicate with ResourceType
export enum OverlayType {
  STORYBOARD = 'storyboard',
  TEXT = 'text',
  VIDEO = 'video',
  SOUND = 'sound',
  NARRATION = 'NARRATION',

  /**
   * TODO: 确认与 TEXT 的区别
   */
  CAPTION = 'caption',

  STICKER = 'sticker',

  material = 'material'
}

// Base overlay properties
export type BaseOverlay = {
  id: number
  durationInFrames: number
  from: number
  height: number
  left: number
  top: number
  width: number
  isDragging: boolean
  rotation: number
  type: OverlayType

  /**
   * 标识该 Overlay 处于第几个分镜中
   */
  storyboardIndex?: number

  /**
   * 淡入时长（秒）
   * 用于视频、音频等媒体类型的淡入效果
   */
  fadeInDuration?: number

  /**
   * 淡出时长（秒）
   * 用于视频、音频等媒体类型的淡出效果
   */
  fadeOutDuration?: number

  /**
   * 缓存到本地的资源路径
   */
  localSrc?: string
}

// Base style properties
type BaseStyles = {
  opacity?: number
  zIndex?: number
  transform?: string
}

// Base animation type
type AnimationConfig = {
  enter?: string
  exit?: string
}

/**
 * 分镜. 每个分镜中包含多个句子, 作为混剪口播的选项
 */
type Storyboard = {
  sentences: string[]
}

/**
 * 脚本. 每个脚本包含多个分镜.
 */
// @ts-ignore
type Scenario = {
  storyboards: Storyboard[]
}

export type TextOverlay = BaseOverlay & {
  type: OverlayType.TEXT
  content: string
  src: string
  styles: BaseStyles & {
    // 基础字体属性
    fontSize: number
    fontWeight: 'bold' | 'normal'
    color: string
    backgroundColor: string
    fontFamily: string
    fontStyle: 'italic' | 'normal'
    lineSpacing?: number
    letterSpacing?: number
    textAlign?: 'left' | 'center' | 'right'
    textShadowDistance?: string

    // 描边属性 (映射自 FontStyleResource.FontStyleContent)
    strokeEnabled?: boolean // 描边开关
    strokeWidth?: number // 描边宽度 (映射自 borderWidth)
    strokeColor?: string // 描边颜色 (映射自 borderColor)

    // 阴影属性 (映射自 FontStyleResource.FontStyleContent)
    shadowEnabled?: boolean // 阴影开关
    shadowBlur?: number // 阴影模糊度
    shadowDistance?: number // 阴影距离
    shadowAngle?: number // 阴影角度
    shadowColor?: string // 阴影颜色
    shadowOpacity?: number // 阴影不透明度 (映射自 shadowColorAlpha)

    // 文字透明度
    textOpacity?: number // 文字不透明度 (映射自 textAlpha)

    // 下划线属性
    underlineEnabled?: boolean
    underlineWidth?: string // 下划线宽度
    underlineOffset?: string // 下划线偏移

    // 背景属性
    backgroundOpacity?: string // 背景透明度 (映射自 backgroundAlpha)

    // 气泡图属性
    backgroundImage?: string // 气泡图背景图片URL
    bubbleTextRect?: [number, number, number, number] // 气泡图中文字的相对位置 [x, y, width, height]

    // 布局属性
    padding?: number
    border?: string
    animation?: AnimationConfig
  }
}

export type VideoOverlay = BaseOverlay & {
  type: OverlayType.VIDEO
  content: string
  src: string
  videoStartTime?: number
  speed?: number
  trimStart?: number
  trimEnd?: number
  styles: BaseStyles & {
    objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
    objectPosition?: string
    volume?: number
    borderRadius?: string
    filter?: string
    boxShadow?: string
    border?: string
    padding?: string
    paddingBackgroundColor?: string
    animation?: AnimationConfig // Using shared type
  }
}

export type SoundOverlay = BaseOverlay & {
  type: OverlayType.SOUND
  content: string
  src: string
  startFromSound?: number
  speed?: number
  styles: BaseStyles & {
    volume?: number
  }
}

export type CaptionWord = {
  word: string
  startMs: number
  endMs: number
  confidence: number
}

export type Caption = {
  text: string
  startMs: number
  endMs: number
  timestampMs: number | null
  confidence: number | null
  words: CaptionWord[]
}

// Update CaptionOverlay to include styling for highlighted words
export interface CaptionStyles {
  fontFamily: string
  fontSize: string
  lineHeight: number
  textAlign: 'left' | 'center' | 'right'
  color: string
  backgroundColor?: string
  background?: string
  backdropFilter?: string
  padding?: string
  fontWeight?: number | string
  letterSpacing?: string
  textShadow?: string
  borderRadius?: string
  transition?: string
  highlightStyle?: {
    backgroundColor?: string
    color?: string
    scale?: number
    fontWeight?: number
    textShadow?: string
    padding?: string
    borderRadius?: string
    transition?: string
    background?: string
    border?: string
    backdropFilter?: string
  }
}

export interface CaptionOverlay extends BaseOverlay {
  type: OverlayType.CAPTION
  captions: Caption[]
  styles?: CaptionStyles
  template?: string
}

// Sticker overlay specific
export type StickerOverlay = BaseOverlay & {
  type: OverlayType.STICKER
  content: string
  src: string
  styles: BaseStyles & {
    fill?: string
    stroke?: string
    strokeWidth?: number
    scale?: number
    filter?: string
    animation?: AnimationConfig

    borderRadius?: string
    objectPosition?: string
    boxShadow?: string
    border?: string
    padding?: string
    paddingBackgroundColor?: string
  }
}

export type StoryboardOverlay = BaseOverlay & {
  type: OverlayType.STORYBOARD
}

/**
 * 口播 Overlay, 为 SoundOverlay 和 TextOverlay 的复合体
 */
export type NarrationOverlay = BaseOverlay & {
  type: OverlayType.NARRATION,

  sounds: SoundOverlay[]
  texts: TextOverlay[]
}

export type Overlay =
  | TextOverlay
  | VideoOverlay
  | SoundOverlay
  | CaptionOverlay
  | StickerOverlay
  | StoryboardOverlay
  | NarrationOverlay

// Zod schema for composition props
export const CompositionProps = z.object({
  overlays: z.array(z.any()), // Replace with your actual Overlay type
  durationInFrames: z.number(),
  width: z.number(),
  height: z.number(),
  fps: z.number(),
  src: z.string(),
})

// Other types remain the same
export const RenderRequest = z.object({
  id: z.string(),
  inputProps: CompositionProps,
})

export const ProgressRequest = z.object({
  bucketName: z.string(),
  id: z.string(),
})

export type ProgressResponse =
  | { type: 'error'; message: string }
  | { type: 'progress'; progress: number }
  | { type: 'done'; url: string; size: number }

export interface WaveformData {
  peaks: number[]
  length: number
}

export type IndexableTrack = Track & { index: number }

export type IndexableOverlay = Overlay & { index: number }

export type RenderableOverlay = Overlay & {
  zIndex: number
}
