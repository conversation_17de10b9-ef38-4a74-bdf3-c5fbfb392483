import { ResourceType, ResourceCacheEntry } from '../resource-cache.types.js'

/**
 * 资源缓存客户端接口
 */
export interface ResourceIPCClient {
  fetch(params: { url: string; type: ResourceType; version?: string; customExt?: string }): Promise<string>;
  clean(params?: { maxAgeSeconds?: number; maxCacheSizeMB?: number }): Promise<void>;
  getInfo(params: { url: string }): Promise<ResourceCacheEntry | undefined>;
  getPath(params: { url: string; type: ResourceType }): Promise<string>;
  getAllCached(): Promise<ResourceCacheEntry[]>;
  isCached(params: { url: string }): Promise<boolean>;
}
