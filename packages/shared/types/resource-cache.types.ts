export enum ResourceType {
  AUDIO = 'audio',
  SOUND = 'sound',
  STICKER = 'sticker',
  FONT = 'font',
  MUSIC = 'music',
  TEXTURE = 'texture',
  BUBBLE = 'bubble',
  MATERIAL = 'material',
  SCRIPT = 'script'
  // 可根据需求扩展更多类型
}

export interface ResourceCacheEntry {
  key: string; // 资源的唯一标识，通常是 URL 的 hash
  url: string; // 原始 CDN URL
  type: ResourceType; // 资源类型
  localPath: string; // 本地缓存路径
  version: string; // 资源版本，用于更新判断
  etag?: string; // ETag 值，用于校验资源是否更新
  lastAccessed: number; // 上次访问时间戳，用于清理
  downloadedAt: number; // 下载完成时间戳
  size: number; // 文件大小（字节）
}

export interface ResourceManifest {
  [key: string]: ResourceCacheEntry; // 以 key 为索引的缓存条目集合
}
